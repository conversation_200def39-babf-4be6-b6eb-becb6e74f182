import { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { getUserById, deleteUser, updateUser } from '@/services/userService';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/button';
import {
  ArrowLeft,
  Trash2,
  Calendar,
  Stethoscope,
  User,
  UserCheck,
  BookOpen,
  BadgeCheck,
  Clock,
  Plus,
  Dumbbell,
} from 'lucide-react';
import DoctorServices from '@/components/doctors/DoctorServices';
import DoctorSchedule from '@/components/doctors/DoctorSchedule';
import DoctorDetails from '@/components/doctors/DoctorDetails';
import PatientDetails from '@/components/patients/PatientDetails';
import PatientAssignedPrograms from '@/components/patients/PatientAssignedPrograms';
import UserBookings from '@/components/bookings/UserBookings';
import ProgramForm from '@/components/programs/ProgramForm';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function UserDetail() {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showVerifyConfirm, setShowVerifyConfirm] = useState(false);
  const [showProgramForm, setShowProgramForm] = useState(false);

  // Fetch user details
  const {
    data: user,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => getUserById(userId!),
    enabled: !!userId,
  });

  // Verify user mutation
  const verifyUserMutation = useMutation({
    mutationFn: (userId: string) => updateUser(userId, { isVerified: true }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user', userId] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setShowVerifyConfirm(false);
    },
    onError: (error) => {
      console.error('Failed to verify user:', error);
    },
  });

  // Handle verify user
  const handleVerifyUser = () => {
    if (!userId) return;
    verifyUserMutation.mutate(userId);
  };

  // Handle delete user
  const handleDeleteUser = async () => {
    if (!userId) return;

    setIsDeleting(true);
    try {
      await deleteUser(userId);
      navigate('/dashboard/users');
    } catch (err) {
      console.error('Failed to delete user:', err);
      setIsDeleting(false);
    }
  };

  // Handle creating and assigning a new program
  const handleProgramFormSuccess = async () => {
    // The ProgramForm handles program creation, after success we close the modal
    setShowProgramForm(false);
    // Invalidate assigned programs query to refresh the list
    queryClient.invalidateQueries({ queryKey: ['assignedPrograms', userId] });
  };



  if (isLoading) {
    return (
      <DashboardLayout title="User Details">
        <div className="flex h-full items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (isError) {
    return (
      <DashboardLayout title="User Details">
        <div className="rounded-md bg-destructive/15 p-4 text-destructive">
          Error loading user: {(error as Error).message}
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="User Details">
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => navigate('/dashboard/users')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">User Details</h1>
          </div>
          <div className="flex space-x-2">
            {/* Show verify button only if user is onboarded but not verified */}
            {user?.isOnboarded && !user?.isVerified && (
              <Button
                variant="default"
                className="w-full text-center sm:w-auto"
                onClick={() => setShowVerifyConfirm(true)}
                disabled={verifyUserMutation.isPending}
              >
                <BadgeCheck className="mr-2 h-4 w-4" />
                {verifyUserMutation.isPending ? 'Verifying...' : 'Mark as Verified'}
              </Button>
            )}
            {/* Show assign program button only for patients */}
            {user?.userType === 'patient' && (
              <Button
                variant="outline"
                className="w-full text-center sm:w-auto cursor-pointer"
                onClick={() => setShowProgramForm(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Assign Program
              </Button>
            )}
            <Button
              variant="destructive"
              className="text-white hover:cursor-pointer w-full text-center sm:w-auto"
              onClick={() => setShowDeleteConfirm(true)}
            >
              <Trash2 className="mr-2 h-4 w-4 text-white" />
              Delete
            </Button>
          </div>
        </div>

        {/* User profile header */}
        <div className="mb-6 rounded-lg border bg-card p-6 shadow-sm">
          <div className="flex items-center space-x-4">
            <div className="h-16 w-16 overflow-hidden rounded-full bg-muted">
              {user?.photo ? (
                <img
                  src={user.photo}
                  alt={user.name}
                  className="h-full w-full object-cover"
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-primary/10 text-xl font-medium text-primary">
                  {user?.name.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <h2 className="text-xl font-semibold">{user?.name}</h2>
                <div className="flex items-center space-x-1">
                  {user?.isVerified ? (
                    <>
                      <BadgeCheck className="h-5 w-5 text-green-600" />
                      {/* <span className="inline-flex rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                        Verified
                      </span> */}
                    </>
                  ) : (
                    <>
                      <Clock className="h-5 w-5 text-yellow-600" />
                      {/* <span className="inline-flex rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                        Pending Verification
                      </span> */}
                    </>
                  )}
                </div>
              </div>
              <p className="text-sm text-muted-foreground">{user?.email}</p>
              <p className="mt-1 text-sm text-muted-foreground capitalize">
                {user?.userType === 'doctor' ? (
                  <span className="flex items-center">
                    <Stethoscope className="mr-1 h-4 w-4 text-primary" />
                    Doctor
                  </span>
                ) : (
                  <span className="flex items-center">
                    <UserCheck className="mr-1 h-4 w-4 text-blue-500" />
                    Patient
                  </span>
                )}
              </p>
            </div>
          </div>
        </div>

        {/* Tabs for user information */}
        <Tabs defaultValue="profile" className="w-full  mb-0 ">
          <TabsList className="mb-4 gap-4 w-full flex-wrap sm:flex-wrap !h-auto">
            <TabsTrigger value="profile" className="flex items-center">
              <User className="mr-2 h-4 w-4" />
              Profile
            </TabsTrigger>
            {user?.userType === 'doctor' && (
              <>
                <TabsTrigger value="services" className="flex items-center">
                  <Stethoscope className="mr-2 h-4 w-4" />
                  Services
                </TabsTrigger>
                <TabsTrigger value="schedule" className="flex items-center">
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule
                </TabsTrigger>
              </>
            )}
            <TabsTrigger value="bookings" className="flex items-center">
              <BookOpen className="mr-2 h-4 w-4" />
              Bookings
            </TabsTrigger>
            {user?.userType === 'patient' && (
              <TabsTrigger value="programs" className="flex items-center">
                <Dumbbell className="mr-2 h-4 w-4" />
                Programs
              </TabsTrigger>
            )}
            
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile" className="space-y-6 mt-0 sm:mt-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="mb-4 text-lg font-medium">Basic Information</h3>
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Phone</div>
                    <div className="text-sm">
                      {user?.phoneNumber || 'Not provided'}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Gender</div>
                    <div className="text-sm capitalize">
                      {user?.gender || 'Not specified'}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Address</div>
                    <div className="text-sm">
                      {user?.address || 'Not provided'}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Verified</div>
                    <div className="text-sm">
                      {user?.isVerified ? 'Yes' : 'No'}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Onboarded</div>
                    <div className="text-sm">
                      {user?.isOnboarded ? 'Yes' : 'No'}
                    </div>
                  </div>
                </div>
              </div>

              {user?.userType === 'doctor' && (
                <div className="rounded-lg border bg-card p-6 shadow-sm">
                  <h3 className="mb-4 text-lg font-medium">
                    Doctor Information
                  </h3>
                  <DoctorDetails doctor={user} />
                </div>
              )}

              {user?.userType === 'patient' && (
                <div className="rounded-lg border bg-card p-6 shadow-sm">
                  <h3 className="mb-4 text-lg font-medium">
                    Patient Information
                  </h3>
                  <PatientDetails patient={user} />
                </div>
              )}
            </div>
          </TabsContent>

          {/* Services Tab - Only for doctors */}
          {user?.userType === 'doctor' && (
            <TabsContent value="services" className="space-y-6">
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <div className="mb-4 flex items-center">
                  <Stethoscope className="mr-2 h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">Services Offered</h3>
                </div>
                <DoctorServices
                  services={user?.services}
                  doctorId={user?._id}
                />
              </div>
            </TabsContent>
          )}

          {/* Schedule Tab - Only for doctors */}
          {user?.userType === 'doctor' && (
            <TabsContent value="schedule" className="space-y-6">
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <div className="mb-4 flex items-center">
                  <Calendar className="mr-2 h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">Weekly Schedule</h3>
                </div>
                <DoctorSchedule schedule={user?.schedule} />
              </div>
            </TabsContent>
          )}

          {/* Bookings Tab */}
          <TabsContent value="bookings" className="space-y-6">
            <div className="rounded-lg border bg-card p-6 shadow-sm">
              {userId && user?.userType && (
                <UserBookings userId={userId} userType={user.userType} />
              )}
            </div>
          </TabsContent>

          {/* Programs Tab - Only for patients */}
          {user?.userType === 'patient' && (
            <TabsContent value="programs" className="space-y-6">
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <PatientAssignedPrograms patientId={userId!} />
              </div>
            </TabsContent>
          )}

          {/* Details Tab */}
          {/* <TabsContent value="details" className="space-y-6">
            <div className="rounded-lg border bg-card p-6 shadow-sm">
              <h3 className="mb-4 text-lg font-medium">Account Details</h3>
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm font-medium">User ID</div>
                  <div className="font-mono text-xs">{user?._id}</div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm font-medium">Created At</div>
                  <div className="text-sm">{formatDate(user?.createdAt)}</div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm font-medium">Updated At</div>
                  <div className="text-sm">{formatDate(user?.updatedAt)}</div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm font-medium">Status</div>
                  <div className="text-sm">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                        user?.status === 'approved'
                          ? 'bg-green-100 text-green-800'
                          : user?.status === 'rejected'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {user?.status || 'pending'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent> */}
        </Tabs>
      </div>

      {/* Delete confirmation dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/60">
          <div className="w-full max-w-md rounded-lg border bg-card p-6 shadow-lg">
            <h3 className="text-lg font-medium">Confirm Deletion</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Are you sure you want to delete this user? This action cannot be
              undone.
            </p>
            <div className="mt-4 flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteUser}
                disabled={isDeleting}
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Verify confirmation dialog */}
      {showVerifyConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/70 ">
          <div className="w-full max-w-md rounded-lg border bg-card p-6 shadow-lg">
            <h3 className="text-lg font-medium">Confirm Verification</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Are you sure you want to mark this user as verified? This will grant them verified status in the system.
            </p>
            <div className="mt-4 flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowVerifyConfirm(false)}
                disabled={verifyUserMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                variant="default"
                onClick={handleVerifyUser}
                disabled={verifyUserMutation.isPending}
              >
                <BadgeCheck className="mr-2 h-4 w-4" />
                {verifyUserMutation.isPending ? 'Verifying...' : 'Verify User'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Create and Assign Program Modal */}
      {user?.userType === 'patient' && (
        <Dialog open={showProgramForm} onOpenChange={setShowProgramForm}>
          <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto sm:w-full">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">
                Assign Program to {user.name}
              </DialogTitle>
            </DialogHeader>
            <ProgramForm
              patientId={userId!}
              patientName={user.name}
              onCancel={() => setShowProgramForm(false)}
              onSuccess={handleProgramFormSuccess}
            />
          </DialogContent>
        </Dialog>
      )}
    </DashboardLayout>
  );
}
