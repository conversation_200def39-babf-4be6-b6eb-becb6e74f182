import { useEffect, useState } from 'react';
import { useUsersStore } from '@/stores/useUsersStore';

interface UserName {
  id: string;
  name: string;
}

interface UseUserNamesReturn {
  userNames: UserName[];
  isLoading: boolean;
  error: string | null;
}

/**
 * Custom hook to fetch and manage user names for a list of user IDs
 * @param userIds - Array of user IDs to fetch names for
 * @returns Object containing userNames array, loading state, and error
 */
export const useUserNames = (userIds: string[]): UseUserNamesReturn => {
  const { getUserNames, getUsers, fetchUsers, isLoading, error } = useUsersStore();
  const [userNames, setUserNames] = useState<UserName[]>([]);
  const [localLoading, setLocalLoading] = useState(false);

  useEffect(() => {
    if (!userIds || userIds.length === 0) {
      setUserNames([]);
      return;
    }

    const fetchUserNames = async () => {
      setLocalLoading(true);
      
      try {
        // First, check if we have cached users
        const cachedUsers = getUsers(userIds);
        const cachedUserNames = cachedUsers
          .filter(user => user !== null)
          .map(user => ({ id: user!._id, name: user!.name }));

        // If we have all users cached, use them
        if (cachedUserNames.length === userIds.length) {
          setUserNames(cachedUserNames);
          setLocalLoading(false);
          return;
        }

        // Otherwise, fetch missing users
        const names = await getUserNames(userIds);
        setUserNames(names);
      } catch (err) {
        console.error('Error fetching user names:', err);
        // Fallback to showing IDs if fetch fails
        setUserNames(userIds.map(id => ({ id, name: id })));
      } finally {
        setLocalLoading(false);
      }
    };

    fetchUserNames();
  }, [userIds, getUserNames, getUsers, fetchUsers]);

  return {
    userNames,
    isLoading: localLoading || isLoading,
    error,
  };
};
