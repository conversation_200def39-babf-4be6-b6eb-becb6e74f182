import { Users, User } from 'lucide-react';
import { useUserNames } from '@/hooks/useUserNames';

interface MembersListProps {
  memberIds: string[];
  className?: string;
}

export default function MembersList({ memberIds, className = '' }: MembersListProps) {
  const { userNames, isLoading, error } = useUserNames(memberIds);

  if (!memberIds || memberIds.length === 0) {
    return (
      <p className="text-muted-foreground">No members in this community.</p>
    );
  }

  if (error) {
    console.error('Error loading member names:', error);
  }

  return (
    <div className={className}>
      <ul className="space-y-2">
        {memberIds.map((memberId) => {
          const userInfo = userNames.find(user => user.id === memberId);
          const displayName = userInfo?.name || memberId;
          const isLoadingThisMember = isLoading && !userInfo;

          return (
            <li
              key={memberId}
              className="rounded-md border border-border bg-background p-3"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <User className="h-4 w-4 text-primary" />
                  </div>
                  <div className="flex flex-col">
                    {isLoadingThisMember ? (
                      <div className="flex items-center space-x-2">
                        <div className="h-4 w-24 bg-muted animate-pulse rounded"></div>
                        <span className="text-xs text-muted-foreground">Loading...</span>
                      </div>
                    ) : (
                      <>
                        <span className="font-medium">{displayName}</span>
                        {/* {userInfo && userInfo.name !== memberId && (
                          <span className="text-xs text-muted-foreground">ID: {memberId}</span>
                        )} */}
                      </>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                  <Users className="h-3 w-3" />
                  <span>Member</span>
                </div>
              </div>
            </li>
          );
        })}
      </ul>
      
      {isLoading && (
        <div className="mt-2 text-xs text-muted-foreground flex items-center space-x-1">
          <div className="h-3 w-3 border border-primary border-t-transparent rounded-full animate-spin"></div>
          <span>Loading member details...</span>
        </div>
      )}
    </div>
  );
}
