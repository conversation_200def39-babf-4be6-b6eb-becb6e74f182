import { type Category, createCategory, updateCategory, checkCategoryNameExists } from '@/services/categoryService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState, useEffect, useCallback } from 'react';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';

interface CategoryFormProps {
  category?: Category;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function CategoryForm({ category, onSuccess, onCancel }: CategoryFormProps) {
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDuplicate, setIsDuplicate] = useState(false);
  const [isCheckingDuplicate, setIsCheckingDuplicate] = useState(false);

  // Initialize form with category data if editing
  useEffect(() => {
    if (category) {
      setName(category.name);
    }
  }, [category]);

  // Debounced duplicate check
  const checkDuplicate = useCallback(async (categoryName: string) => {
    if (!categoryName.trim() || categoryName.trim().length < 2) {
      setIsDuplicate(false);
      return;
    }

    setIsCheckingDuplicate(true);
    try {
      const exists = await checkCategoryNameExists(categoryName.trim(), category?.id);
      setIsDuplicate(exists);
    } catch (error) {
      console.error('Error checking duplicate:', error);
    } finally {
      setIsCheckingDuplicate(false);
    }
  }, [category?.id]);

  // Debounce the duplicate check
  useEffect(() => {
    const timer = setTimeout(() => {
      if (name.trim()) {
        checkDuplicate(name);
      } else {
        setIsDuplicate(false);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [name, checkDuplicate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim() || name.trim().length < 2) {
      setError('Category name must be at least 2 characters long');
      return;
    }

    if (isDuplicate) {
      setError('A category with this name already exists');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (category?.id) {
        // Update existing category
        const updateData = {
          name: name.trim(),
        };

        await updateCategory(category.id, updateData);
      } else {
        // Create new category
        const newCategoryData = {
          name: name.trim(),
        };

        await createCategory(newCategoryData);
      }

      onSuccess();
    } catch (err) {
      console.error(`Error ${category?.id ? 'updating' : 'creating'} category:`, err);
      setError(err instanceof Error ? err.message : `Failed to ${category?.id ? 'update' : 'create'} category`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Category Name</Label>
          <div className="relative">
            <Input
              id="name"
              type="text"
              placeholder="Enter category name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={isLoading}
              className={`pr-10 ${
                isDuplicate ? 'border-red-500 focus:border-red-500' : 
                name.trim() && !isDuplicate && !isCheckingDuplicate ? 'border-green-500 focus:border-green-500' : ''
              }`}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              {isCheckingDuplicate ? (
                <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
              ) : isDuplicate ? (
                <AlertCircle className="h-4 w-4 text-red-500" />
              ) : name.trim() && name.trim().length >= 2 ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : null}
            </div>
          </div>
          
          {/* Validation messages */}
          {name.trim() && name.trim().length < 2 && (
            <div className="flex items-center gap-2 text-sm text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span>Category name must be at least 2 characters long</span>
            </div>
          )}
          
          {isDuplicate && (
            <div className="flex items-center gap-2 text-sm text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span>A category with this name already exists</span>
            </div>
          )}
          
          {name.trim() && !isDuplicate && !isCheckingDuplicate && name.trim().length >= 2 && (
            <div className="flex items-center gap-2 text-sm text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span>Category name is available</span>
            </div>
          )}

          <p className="text-sm text-muted-foreground">
            Category names will be automatically converted to lowercase.
          </p>

          {error && (
            <div className="flex items-center gap-2 text-sm text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              isLoading ||
              isDuplicate ||
              !name.trim() ||
              name.trim().length < 2 ||
              isCheckingDuplicate
            }
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {category ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              category ? 'Update Category' : 'Create Category'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
