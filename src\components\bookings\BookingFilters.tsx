import { useState } from 'react';
import { Button } from '@/components/ui/button';
// import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar as CalendarIcon, X } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import type { BookingFilterParams } from '@/services/bookingService';

interface BookingFiltersProps {
  filters: BookingFilterParams;
  onFilterChange: (filters: BookingFilterParams) => void;
  showPatientFilter?: boolean;
  showDoctorFilter?: boolean;
}

export default function BookingFilters({
  filters,
  onFilterChange,
  // showPatientFilter = true,
  // showDoctorFilter = true,
}: BookingFiltersProps) {
  const [date, setDate] = useState<Date | undefined>(
    filters.date ? new Date(filters.date) : undefined
  );

  // Handle status change
  const handleStatusChange = (value: string) => {
    onFilterChange({
      ...filters,
      status: value === 'all' ? undefined : (value as 'active' | 'completed'),
      page: 1,
    });
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    setDate(date);
    onFilterChange({
      ...filters,
      date: date ? format(date, 'yyyy-MM-dd') : undefined,
      page: 1,
    });
  };

  // Handle past bookings toggle
  const handlePastBookingsChange = (value: string) => {
    onFilterChange({
      ...filters,
      isPast: value === 'all' ? undefined : value === 'true',
      page: 1,
    });
  };

  // Clear all filters
  const handleClearFilters = () => {
    setDate(undefined);
    onFilterChange({
      page: 1,
      limit: filters.limit,
      // Keep the patient/doctor filter if it's set from the parent component
      ...(filters.patient ? { patient: filters.patient } : {}),
      ...(filters.doctor ? { doctor: filters.doctor } : {}),
    });
  };

  return (
    <div className="mb-4 space-y-4">
      <div className="flex flex-col gap-4 sm:flex-row">
        {/* Status Filter */}
        <Select
          value={filters.status || 'all'}
          onValueChange={handleStatusChange}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
          </SelectContent>
        </Select>

        {/* Date Filter */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-start text-left font-normal sm:w-[180px]',
                !date && 'text-muted-foreground'
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, 'PPP') : <span>Pick a date</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={date}
              onSelect={handleDateChange}
              initialFocus
            />
          </PopoverContent>
        </Popover>

        {/* Past Bookings Filter */}
        <Select
          value={filters.isPast !== undefined ? filters.isPast.toString() : 'all'}
          onValueChange={handlePastBookingsChange}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Booking Time" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Bookings</SelectItem>
            <SelectItem value="true">Past Bookings</SelectItem>
            <SelectItem value="false">Upcoming Bookings</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters Button */}
        <Button
          variant="ghost"
          onClick={handleClearFilters}
          className="w-full sm:w-auto"
        >
          <X className="mr-2 h-4 w-4" />
          Clear Filters
        </Button>
      </div>
    </div>
  );
}
