import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getUsers } from '@/services/userService';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Users, UserPlus, UserCheck, UserX } from 'lucide-react';

export default function Dashboard() {
  const navigate = useNavigate();

  // Fetch users with counts by type
  const { data: usersData, isLoading } = useQuery({
    queryKey: ['users', { limit: 1 }],
    queryFn: () => getUsers({ limit: 1 }),
  });

  // Fetch doctors count
  const { data: doctorsData, isLoading: isLoadingDoctors } = useQuery({
    queryKey: ['users', { userType: 'doctor' }],
    queryFn: () => getUsers({ userType: 'doctor', limit: 1 }),
  });

  // Fetch patients count
  const { data: patientsData, isLoading: isLoadingPatients } = useQuery({
    queryKey: ['users', { userType: 'patient' }],
    queryFn: () => getUsers({ userType: 'patient', limit: 1 }),
  });

  // Fetch pending approvals count
  const { data: pendingData, isLoading: isLoadingPending } = useQuery({
    queryKey: ['users', { status: 'pending' }],
    queryFn: () => getUsers({ status: 'pending', limit: 1 }),
  });

  // Navigate to users list
  const handleViewUsers = () => {
    navigate('/dashboard/users');
  };

  return (
    <DashboardLayout title="Dashboard">
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Welcome to OW Admin</h1>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Total Users Card */}
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Total Users
                </p>
                <h3 className="mt-1 text-2xl font-bold">
                  {isLoading ? (
                    <div className="h-6 w-12 animate-pulse rounded bg-muted"></div>
                  ) : (
                    usersData?.total || 0
                  )}
                </h3>
              </div>
              <div className="rounded-full bg-primary/10 p-3 text-primary">
                <Users className="h-5 w-5" />
              </div>
            </div>
            <Button
              variant="ghost"
              className="mt-4 w-full justify-start px-0 text-sm text-muted-foreground"
              onClick={handleViewUsers}
            >
              View all users
            </Button>
          </div>

          {/* Patients Card */}
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Patients
                </p>
                <h3 className="mt-1 text-2xl font-bold">
                  {isLoadingPatients ? (
                    <div className="h-6 w-12 animate-pulse rounded bg-muted"></div>
                  ) : (
                    patientsData?.total || 0
                  )}
                </h3>
              </div>
              <div className="rounded-full bg-blue-100 p-3 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400">
                <UserCheck className="h-5 w-5" />
              </div>
            </div>
            <Button
              variant="ghost"
              className="mt-4 w-full justify-start px-0 text-sm text-muted-foreground"
              onClick={() => navigate('/dashboard/users/patients')}
            >
              View patients
            </Button>
          </div>

          {/* Doctors Card */}
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Doctors
                </p>
                <h3 className="mt-1 text-2xl font-bold">
                  {isLoadingDoctors ? (
                    <div className="h-6 w-12 animate-pulse rounded bg-muted"></div>
                  ) : (
                    doctorsData?.total || 0
                  )}
                </h3>
              </div>
              <div className="rounded-full bg-green-100 p-3 text-green-600 dark:bg-green-900/20 dark:text-green-400">
                <UserPlus className="h-5 w-5" />
              </div>
            </div>
            <Button
              variant="ghost"
              className="mt-4 w-full justify-start px-0 text-sm text-muted-foreground"
              onClick={() => navigate('/dashboard/users/doctors')}
            >
              View doctors
            </Button>
          </div>

          {/* Pending Approvals Card */}
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Pending Approvals
                </p>
                <h3 className="mt-1 text-2xl font-bold">
                  {isLoadingPending ? (
                    <div className="h-6 w-12 animate-pulse rounded bg-muted"></div>
                  ) : (
                    pendingData?.total || 0
                  )}
                </h3>
              </div>
              <div className="rounded-full bg-yellow-100 p-3 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400">
                <UserX className="h-5 w-5" />
              </div>
            </div>
            <Button
              variant="ghost"
              className="mt-4 w-full justify-start px-0 text-sm text-muted-foreground"
              onClick={() => navigate('/dashboard/users?status=pending')}
            >
              View pending approvals
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Quick Actions */}
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <h3 className="mb-4 text-lg font-medium">Quick Actions</h3>
            <div className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => navigate('/dashboard/users?status=pending')}
              >
                <UserCheck className="mr-2 h-4 w-4" />
                Review Pending Approvals
              </Button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <h3 className="mb-4 text-lg font-medium">Recent Activity</h3>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                No recent activity to display.
              </p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
