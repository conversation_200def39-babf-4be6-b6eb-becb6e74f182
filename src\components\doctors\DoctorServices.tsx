import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateUser } from '@/services/userService';
import EditableServicePrice from './EditableServicePrice';
import { Save } from 'lucide-react';

interface Service {
  name: string;
  sessionTime: number | string;
  price: string;
  description?: string;
}

interface DoctorServicesProps {
  services?: Service[] | any;
  doctorId?: string;
}

export default function DoctorServices({
  services,
  doctorId,
}: DoctorServicesProps) {
  const queryClient = useQueryClient();
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [modifiedServices, setModifiedServices] = useState<Service[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize modifiedServices when component mounts or services change
  useEffect(() => {
    if (services) {
      const servicesList = Array.isArray(services) ? [...services] : [services];
      setModifiedServices(servicesList);
    }
  }, [services]);

  const updateUserMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      updateUser(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user', doctorId] });
      setHasChanges(false);
      setEditingIndex(null);
    },
  });

  if (!services || services.length === 0) {
    return (
      <div className="text-sm text-muted-foreground">No services available</div>
    );
  }

  // Ensure services is an array
  const servicesList = Array.isArray(services) ? services : [services];

  const handlePriceEdit = (index: number) => {
    setEditingIndex(index);
  };

  const handlePriceSave = (index: number, newPrice: string) => {
    const updatedServices = [...modifiedServices];
    updatedServices[index] = {
      ...updatedServices[index],
      price: newPrice,
    };

    setModifiedServices(updatedServices);
    setEditingIndex(null);
    setHasChanges(true);
  };

  const handlePriceCancel = () => {
    setEditingIndex(null);
  };

  const saveAllChanges = () => {
    if (doctorId) {
      updateUserMutation.mutate({
        id: doctorId,
        data: { services: modifiedServices },
      });
    }
  };

  return (
    <div>
      {hasChanges && (
        <div className="mb-4 flex justify-end">
          <Button
            onClick={saveAllChanges}
            disabled={updateUserMutation.isPending}
            className="flex items-center"
          >
            <Save className="mr-2 h-4 w-4" />
            {updateUserMutation.isPending ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      )}

      <div className="space-y-4">
        {servicesList.map((service, index) => (
          <div key={index} className="rounded-md border p-4">
            <div className="mb-2 flex items-center justify-between">
              <h4 className="font-medium">{service.name}</h4>
              <EditableServicePrice
                price={modifiedServices[index]?.price || service.price}
                onSave={(newPrice) => handlePriceSave(index, newPrice)}
                onCancel={handlePriceCancel}
                isEditing={editingIndex === index}
                onEdit={() => handlePriceEdit(index)}
              />
            </div>
            <div className="mb-2 text-sm text-muted-foreground">
              <span className="inline-flex items-center rounded-md bg-muted px-2 py-1 text-xs">
                {service.sessionTime} min session
              </span>
            </div>
            {service.description && (
              <p className="text-sm text-muted-foreground">
                {service.description}
              </p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
