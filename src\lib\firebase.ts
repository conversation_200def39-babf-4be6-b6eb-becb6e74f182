import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getStorage } from 'firebase/storage';


const firebaseConfig = {
  apiKey: "AIzaSyAsoou1NwouxS0j2j7TNyV5BUC3MqYg0cc",
  authDomain: "ow-app-33024.firebaseapp.com",
  projectId: "ow-app-33024",
  storageBucket: "ow-app-33024.firebasestorage.app",
  messagingSenderId: "1048194896885",
  appId: "1:1048194896885:web:301e44932507092fa286bc",
  measurementId: "G-3WQD7TBNEW"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore
export const db = getFirestore(app);

// Initialize Auth
export const auth = getAuth(app);

// Initialize Storage
export const storage = getStorage(app);

export default app;
