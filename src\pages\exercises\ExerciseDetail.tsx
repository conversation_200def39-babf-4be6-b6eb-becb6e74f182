import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getExerciseById, deleteExercise } from "@/services/exerciseService";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, Trash2, Clock } from "lucide-react";
import ExerciseModal from "@/components/exercises/ExerciseModal";

export default function ExerciseDetail() {
  const { exerciseId } = useParams<{ exerciseId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch exercise details
  const {
    data: exercise,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["exercise", exerciseId],
    queryFn: () => getExerciseById(exerciseId!),
    enabled: !!exerciseId,
  });

  const handleDelete = async () => {
    if (!exercise?.id) return;

    if (!confirm("Are you sure you want to delete this exercise? This action cannot be undone.")) {
      return;
    }

    setIsDeleting(true);
    try {
      await deleteExercise(exercise.id);
      queryClient.invalidateQueries({ queryKey: ["exercises"] });
      navigate("/dashboard/exercises");
    } catch (error) {
      console.error("Error deleting exercise:", error);
      alert("Failed to delete exercise. Please try again.");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleEditSuccess = () => {
    setIsModalOpen(false);
    queryClient.invalidateQueries({ queryKey: ["exercise", exerciseId] });
    queryClient.invalidateQueries({ queryKey: ["exercises"] });
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  if (isLoading) {
    return (
      <DashboardLayout title="Exercise Details">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">Loading exercise details...</div>
        </div>
      </DashboardLayout>
    );
  }

  if (isError || !exercise) {
    return (
      <DashboardLayout title="Exercise Details">
        <div className="flex items-center justify-center h-64">
          <div className="text-center text-red-500">
            {error instanceof Error ? error.message : "Exercise not found"}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title={exercise.exerciseName}>
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/dashboard/exercises")}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">{exercise.exerciseName}</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => setIsModalOpen(true)}
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Delete
            </Button>
          </div>
        </div>

        {/* Exercise Modal */}
        <ExerciseModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          exercise={exercise}
          onSuccess={handleEditSuccess}
        />

        {/* Exercise Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Media */}
          <div className="space-y-6">
              {/* Thumbnail */}
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4">Thumbnail</h3>
                <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={exercise.thumbnail}
                    alt={exercise.exerciseName}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIyNSIgdmlld0JveD0iMCAwIDQwMCAyMjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjI1IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMDAgMTYyLjVDMjI3LjYxNCAxNjIuNSAyNTAgMTQwLjExNCAyNTAgMTEyLjVDMjUwIDg0Ljg4NTggMjI3LjYxNCA2Mi41IDIwMCA2Mi41QzE3Mi4zODYgNjIuNSAxNTAgODQuODg1OCAxNTAgMTEyLjVDMTUwIDE0MC4xMTQgMTcyLjM4NiAxNjIuNSAyMDAgMTYyLjVaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iNCIvPgo8cGF0aCBkPSJNMjAwIDEzNy41QzIxMy44MDcgMTM3LjUgMjI1IDEyNi4zMDcgMjI1IDExMi41QzIyNSA5OC42OTI5IDIxMy44MDcgODcuNSAyMDAgODcuNUMxODYuMTkzIDg3LjUgMTc1IDk4LjY5MjkgMTc1IDExMi41QzE3NSAxMjYuMzA3IDE4Ni4xOTMgMTM3LjUgMjAwIDEzNy41WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                    }}
                  />
                </div>
                
              </div>

              {/* Video */}
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4">Exercise Video</h3>
                <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                  <video
                    controls
                    className="w-full h-full"
                    poster={exercise.thumbnail}
                  >
                    <source src={exercise.videoUrl} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
               
              </div>
            </div>

            {/* Right Column - Details */}
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4">Exercise Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Exercise Name</label>
                    <p className="text-lg font-medium">{exercise.exerciseName}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-500">Type</label>
                    <div className="mt-1">
                      <span className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
                        {exercise.type}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500">Duration</label>
                    <div className="flex items-center mt-1">
                      <Clock className="mr-2 h-5 w-5 text-gray-500" />
                      <span className="text-lg font-medium">{exercise.duration} minutes</span>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500">Created At</label>
                    <p className="text-lg">
                      {exercise.createdAt && typeof exercise.createdAt.toDate === "function"
                        ? new Date(exercise.createdAt.toDate()).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })
                        : "N/A"}
                    </p>
                  </div>

                  {exercise.updatedAt && exercise.updatedAt !== exercise.createdAt && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Last Updated</label>
                      <p className="text-lg">
                        {typeof exercise.updatedAt.toDate === "function"
                          ? new Date(exercise.updatedAt.toDate()).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })
                          : "N/A"}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* URLs */}
            
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
