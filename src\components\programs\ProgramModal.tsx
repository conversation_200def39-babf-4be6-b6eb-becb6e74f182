import { type Program } from '@/services/programService';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import ProgramForm from './ProgramForm';

interface ProgramModalProps {
  isOpen: boolean;
  onClose: () => void;
  program?: Program;
  onSuccess: () => void;
}

export default function ProgramModal({ 
  isOpen, 
  onClose, 
  program, 
  onSuccess 
}: ProgramModalProps) {
  const handleSuccess = () => {
    onSuccess();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto sm:w-full">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl">
            {program ? 'Edit Program' : 'Add New Program'}
          </DialogTitle>
        </DialogHeader>
        <ProgramForm
          program={program}
          onCancel={onClose}
          onSuccess={handleSuccess}
        />
      </DialogContent>
    </Dialog>
  );
}
