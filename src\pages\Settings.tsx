import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  Button,
  Input,
  Label,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui';
import { Save } from 'lucide-react';
import { getSettings, updateSettings } from '@/services/settingsService';

export default function Settings() {
  const queryClient = useQueryClient();
  const [feePercentage, setFeePercentage] = useState<number>(20);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch current settings
  const { data: settings, isLoading: isLoadingSettings } = useQuery({
    queryKey: ['settings', 'applicationFeePercentage'],
    queryFn: () => getSettings('applicationFeePercentage'),
  });

  // Update settings when data is loaded
  useEffect(() => {
    if (settings) {
      setFeePercentage(settings.value);
    }
  }, [settings]);

  // Mutation for updating settings
  const updateSettingsMutation = useMutation({
    mutationFn: (data: { key: string; value: any }) =>
      updateSettings(data.key, data.value),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings'] });
      setSuccess('Application fee percentage updated successfully');
      setTimeout(() => setSuccess(null), 3000);
    },
    onError: (error: any) => {
      setError(error.message || 'Failed to update settings');
      setTimeout(() => setError(null), 3000);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    // Validate input
    const fee = Number(feePercentage);
    if (isNaN(fee) || fee < 0 || fee > 100) {
      setError('Fee percentage must be a number between 0 and 100');
      setIsLoading(false);
      return;
    }

    // Update settings
    updateSettingsMutation.mutate({
      key: 'applicationFeePercentage',
      value: fee,
    });
    setIsLoading(false);
  };

  return (
    <DashboardLayout title="Settings">
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Application Settings</CardTitle>
            <CardDescription>
              Configure global application settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Fee Percentage */}
              <div className="space-y-2">
                <Label htmlFor="feePercentage">Application Fee Percentage</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="feePercentage"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={feePercentage}
                    onChange={(e) => setFeePercentage(Number(e.target.value))}
                    className="max-w-[120px]"
                    disabled={isLoadingSettings}
                  />
                  <span>%</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  This is the percentage fee that will be applied to all transactions.
                </p>
              </div>

              {/* Error and Success messages */}
              {error && (
                <div className="rounded-md bg-destructive/15 p-4 text-destructive">
                  {error}
                </div>
              )}
              {success && (
                <div className="rounded-md bg-green-100 p-4 text-green-800">
                  {success}
                </div>
              )}

              {/* Submit button */}
              <Button
                type="submit"
                disabled={isLoading || isLoadingSettings}
                className="flex items-center"
              >
                <Save className="mr-2 h-4 w-4" />
                Save Settings
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
