import * as React from "react";

const Slot = React.forwardRef<HTMLElement, React.HTMLAttributes<HTMLElement> & { asChild?: boolean }>(
  ({ children, asChild = false, ...props }, ref) => {
    if (asChild && React.isValidElement(children)) {
      return React.cloneElement(children, {
        ...props,
        // Cast to any because ref is not a valid prop for function components
        ...(ref ? { ref } : {}),
      } as any);
    }

    return (
      <span {...props} ref={ref as React.RefObject<HTMLSpanElement>}>
        {children}
      </span>
    );
  },
);

Slot.displayName = "Slot";

export { Slot };
