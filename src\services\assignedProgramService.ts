import {
  Timestamp,
  collection,
  addDoc,
  getDocs,
  doc,
  deleteDoc,
  updateDoc,
  query,
  where
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Collections } from '@/constant';
import { type Program } from './programService';

// Define the AssignedProgram interface
export interface AssignedProgram {
  id?: string;
  programId: string;
  patientId: string;
  programData: Program; 
  assignedAt: Timestamp;

  status: 'active' | 'completed' | 'paused';
}

// Assign a program to a patient
export const assignProgramToPatient = async (
  programData: Program,
  patientId: string,
  
): Promise<void> => {
  try {
    const assignedProgramsRef = collection(db, Collections.ASSIGNED_PROGRAMS);
    
    // Check if this program is already assigned to this patient
    const existingQuery = query(
      assignedProgramsRef,
      where('programId', '==', programData.id),
      where('patientId', '==', patientId),
      where('status', '==', 'active')
    );
    
    const existingSnapshot = await getDocs(existingQuery);
    
    if (!existingSnapshot.empty) {
      throw new Error('This program is already assigned to the patient');
    }

    const assignedProgramData: Omit<AssignedProgram, 'id'> = {
      programId: programData.id!,
      patientId,
      programData,
      assignedAt: Timestamp.now(),
      status: 'active'
    };

    await addDoc(assignedProgramsRef, assignedProgramData);
  } catch (error) {
    console.error('Error assigning program to patient:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to assign program to patient');
  }
};

// Get all programs assigned to a specific patient
export const getAssignedPrograms = async (patientId: string): Promise<AssignedProgram[]> => {
  try {
    const assignedProgramsRef = collection(db, Collections.ASSIGNED_PROGRAMS);
    const q = query(
      assignedProgramsRef,
      where('patientId', '==', patientId)
    );
    
    const querySnapshot = await getDocs(q);
    
    const assignedPrograms: AssignedProgram[] = [];
    querySnapshot.forEach((doc) => {
      assignedPrograms.push({
        id: doc.id,
        ...doc.data()
      } as AssignedProgram);
    });

    assignedPrograms.sort((a, b) => {
      const aTime = a.assignedAt?.seconds || 0;
      const bTime = b.assignedAt?.seconds || 0;
      return bTime - aTime;
    });

    return assignedPrograms;
  } catch (error) {
    console.error('Error fetching assigned programs:', error);
    throw new Error('Failed to fetch assigned programs');
  }
};

// Remove an assigned program
export const removeAssignedProgram = async (assignedProgramId: string): Promise<void> => {
  try {
    const assignedProgramRef = doc(db, Collections.ASSIGNED_PROGRAMS, assignedProgramId);
    await deleteDoc(assignedProgramRef);
  } catch (error) {
    console.error('Error removing assigned program:', error);
    throw new Error('Failed to remove assigned program');
  }
};

// Update assigned program status
export const updateAssignedProgramStatus = async (
  assignedProgramId: string,
  status: 'active' | 'completed' | 'paused'
): Promise<void> => {
  try {
    const assignedProgramRef = doc(db, Collections.ASSIGNED_PROGRAMS, assignedProgramId);
    await updateDoc(assignedProgramRef, { status });
  } catch (error) {
    console.error('Error updating assigned program status:', error);
    throw new Error('Failed to update assigned program status');
  }
};

export const createAndAssignProgram = async (
  programData: Omit<Program, 'id' | 'createdAt' | 'updatedAt' | 'duration'>,
  patientId: string,
  duration: number
): Promise<void> => {
  try {
    const assignedProgramsRef = collection(db, Collections.ASSIGNED_PROGRAMS);
    
    const now = Timestamp.now();
    
    
    const assignedProgramData: Omit<AssignedProgram, 'id'> = {
      programId: '', 
      patientId,
      programData: {
        id: '', 
        ...programData,
        duration,
        createdAt: now,
        updatedAt: now,
      },
      assignedAt: now,
      status: 'active'
    };

    // Add the document and get the auto-generated ID
    const docRef = await addDoc(assignedProgramsRef, assignedProgramData);
    
    // Update the document with the correct programId and programData.id
    await updateDoc(docRef, {
      programId: docRef.id,
      'programData.id': docRef.id
    });
  } catch (error) {
    console.error('Error creating and assigning program:', error);
    throw new Error('Failed to create and assign program to patient');
  }
};
