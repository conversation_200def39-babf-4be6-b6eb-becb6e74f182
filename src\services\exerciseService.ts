import {
  Timestamp,
  collection,
  addDoc,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Collections } from '@/constant';

// Define the Exercise interface
export interface Exercise {
  id?: string;
  exerciseName: string;
  thumbnail: string; // URL to thumbnail image
  type: string; // Exercise type (e.g., "cardio", "strength", "flexibility")
  videoUrl: string; // URL to exercise video
  duration: number; // Duration in minutes
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Get all exercises
export const getExercises = async (): Promise<Exercise[]> => {
  try {
    const exercisesRef = collection(db, Collections.EXERCISES);
    const q = query(exercisesRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);

    const exercises: Exercise[] = [];
    querySnapshot.forEach((doc) => {
      exercises.push({
        id: doc.id,
        ...doc.data()
      } as Exercise);
    });

    return exercises;
  } catch (error) {
    console.error('Error fetching exercises:', error);
    throw new Error('Failed to fetch exercises');
  }
};

// Get a single exercise by ID
export const getExerciseById = async (id: string): Promise<Exercise | null> => {
  try {
    const exerciseRef = doc(db, Collections.EXERCISES, id);
    const exerciseSnap = await getDoc(exerciseRef);

    if (exerciseSnap.exists()) {
      return {
        id: exerciseSnap.id,
        ...exerciseSnap.data()
      } as Exercise;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error fetching exercise:', error);
    throw new Error('Failed to fetch exercise');
  }
};

// Check if an exercise name already exists
export const checkExerciseNameExists = async (exerciseName: string, excludeId?: string): Promise<boolean> => {
  try {
    const exercisesRef = collection(db, Collections.EXERCISES);
    const q = query(
      exercisesRef,
      where('exerciseName', '==', exerciseName.trim())
    );
    const querySnapshot = await getDocs(q);

    // If excludeId is provided (for updates), check if any other document has this exercise name
    if (excludeId) {
      return querySnapshot.docs.some(doc => doc.id !== excludeId);
    }

    // For new exercises, any existing document with this exercise name is a duplicate
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking exercise name:', error);
    throw new Error('Failed to check exercise name');
  }
};

// Create a new exercise
export const createExercise = async (exerciseData: Omit<Exercise, 'id' | 'createdAt' | 'updatedAt'>): Promise<Exercise> => {
  try {
    // Check if exercise name already exists
    const nameExists = await checkExerciseNameExists(exerciseData.exerciseName);
    if (nameExists) {
      throw new Error('An exercise with this name already exists');
    }

    const now = Timestamp.now();
    const newExerciseData = {
      ...exerciseData,
      createdAt: now,
      updatedAt: now,
    };

    const exercisesRef = collection(db, Collections.EXERCISES);
    const docRef = await addDoc(exercisesRef, newExerciseData);

    return {
      id: docRef.id,
      ...newExerciseData,
    };
  } catch (error) {
    console.error('Error creating exercise:', error);
    // Re-throw the error with the original message if it's our custom validation error
    if (error instanceof Error && error.message === 'An exercise with this name already exists') {
      throw error;
    }
    throw new Error('Failed to create exercise');
  }
};

// Update an existing exercise
export const updateExercise = async (id: string, exerciseData: Partial<Omit<Exercise, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> => {
  try {
    // Check if exercise name already exists (excluding current exercise)
    if (exerciseData.exerciseName) {
      const nameExists = await checkExerciseNameExists(exerciseData.exerciseName, id);
      if (nameExists) {
        throw new Error('An exercise with this name already exists');
      }
    }

    const exerciseRef = doc(db, Collections.EXERCISES, id);
    const updateData = {
      ...exerciseData,
      updatedAt: Timestamp.now(),
    };

    await updateDoc(exerciseRef, updateData);
  } catch (error) {
    console.error('Error updating exercise:', error);
    // Re-throw the error with the original message if it's our custom validation error
    if (error instanceof Error && error.message === 'An exercise with this name already exists') {
      throw error;
    }
    throw new Error('Failed to update exercise');
  }
};

// Delete an exercise
export const deleteExercise = async (id: string): Promise<void> => {
  try {
    const exerciseRef = doc(db, Collections.EXERCISES, id);
    await deleteDoc(exerciseRef);
  } catch (error) {
    console.error('Error deleting exercise:', error);
    throw new Error('Failed to delete exercise');
  }
};
