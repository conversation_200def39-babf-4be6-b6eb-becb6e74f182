import { useQuery } from "@tanstack/react-query";
import { getCategories } from "@/services/categoryService";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Loader2, AlertCircle, Tag } from "lucide-react";

interface CategorySelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  allowCustom?: boolean; // Allow typing custom values
  onCustomValueChange?: (value: string) => void; // For custom input fallback
}

export default function CategorySelect({
  value,
  onValueChange,
  placeholder = "Select a category",
  label,
  required = false,
  disabled = false,
  className = "",
  allowCustom = false,
  onCustomValueChange,
}: CategorySelectProps) {
  const {
    data: categories = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["categories"],
    queryFn: getCategories,
  });

  // If loading, show loading state
  if (isLoading) {
    return (
      <div className={`space-y-2 ${className}`}>
        {label && (
          <Label>
            {label} {required && <span className="text-destructive">*</span>}
          </Label>
        )}
        <div className="flex items-center space-x-2 h-10 w-full rounded-md border border-input bg-background px-3 py-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm text-muted-foreground">Loading categories...</span>
        </div>
      </div>
    );
  }

  // If error loading categories, show fallback input if allowCustom is true
  if (isError) {
    if (allowCustom && onCustomValueChange) {
      return (
        <div className={`space-y-2 ${className}`}>
          {label && (
            <Label>
              {label} {required && <span className="text-destructive">*</span>}
            </Label>
          )}
          <div className="space-y-2">
            <input
              type="text"
              value={value}
              onChange={(e) => onCustomValueChange(e.target.value)}
              placeholder={placeholder}
              disabled={disabled}
              required={required}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
            <div className="flex items-center space-x-2 text-sm text-amber-600">
              <AlertCircle className="h-4 w-4" />
              <span>Categories unavailable. Using text input.</span>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className={`space-y-2 ${className}`}>
        {label && (
          <Label>
            {label} {required && <span className="text-destructive">*</span>}
          </Label>
        )}
        <div className="flex items-center space-x-2 h-10 w-full rounded-md border border-red-200 bg-red-50 px-3 py-2">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <span className="text-sm text-red-600">
            Error loading categories: {error instanceof Error ? error.message : "Unknown error"}
          </span>
        </div>
      </div>
    );
  }

  // If no categories available, show fallback input if allowCustom is true
  if (categories.length === 0) {
    if (allowCustom && onCustomValueChange) {
      return (
        <div className={`space-y-2 ${className}`}>
          {label && (
            <Label>
              {label} {required && <span className="text-destructive">*</span>}
            </Label>
          )}
          <div className="space-y-2">
            <input
              type="text"
              value={value}
              onChange={(e) => onCustomValueChange(e.target.value)}
              placeholder={placeholder}
              disabled={disabled}
              required={required}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
            <div className="flex items-center space-x-2 text-sm text-blue-600">
              <Tag className="h-4 w-4" />
              <span>No categories available. Using text input.</span>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className={`space-y-2 ${className}`}>
        {label && (
          <Label>
            {label} {required && <span className="text-destructive">*</span>}
          </Label>
        )}
        <div className="flex items-center space-x-2 h-10 w-full rounded-md border border-amber-200 bg-amber-50 px-3 py-2">
          <Tag className="h-4 w-4 text-amber-500" />
          <span className="text-sm text-amber-600">No categories available</span>
        </div>
      </div>
    );
  }

  // Normal select with categories
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label>
          {label} {required && <span className="text-destructive">*</span>}
        </Label>
      )}
      <Select value={value} onValueChange={onValueChange} disabled={disabled} required={required}>
        <SelectTrigger>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {categories.map((category) => (
            <SelectItem key={category.id} value={category.name}>
              <div className="flex items-center space-x-2">
                <Tag className="h-4 w-4" />
                <span className="capitalize">{category.name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
