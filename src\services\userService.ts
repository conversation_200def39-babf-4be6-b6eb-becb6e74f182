import api from '@/lib/api';

// Define the User interface based on the backend schema
export interface Service {
  name: string;
  sessionTime: number;
  price: string;
  description?: string;
}

export interface ScheduleDay {
  day: string;
  from: string;
  to: string;
}

export interface User {
  _id: string;
  name: string;
  email: string;
  phoneNumber?: string;
  address?: string;
  gender?: 'male' | 'female';
  userType: 'patient' | 'doctor' | 'admin';
  status?: 'pending' | 'approved' | 'rejected';
  isVerified: boolean;
  isOnboarded: boolean;
  photo?: string;
  introduction?: string;
  createdAt?: string;
  updatedAt?: string;
  services?: Service[];
  schedule?: ScheduleDay[];
  workExperience?: {
    years?: number;
    patients?: number;
    specialization?: string;
  };
}

// Define the filter parameters for fetching users
export interface UserFilterParams {
  page?: number;
  limit?: number;
  search?: string;
  userType?: string;
  status?: string;
}

// Define the response structure for paginated results
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Get all users with pagination and filtering
export const getUsers = async (
  params: UserFilterParams = {}
): Promise<PaginatedResponse<User>> => {
  const response = await api.get('/users', { params });
  // Transform the response to match the expected structure
  return {
    data: response.data.results || [],
    total: response.data.meta?.totalRecords || 0,
    page: response.data.meta?.page || 1,
    limit: response.data.meta?.limit || 10,
    totalPages: Math.ceil(
      (response.data.meta?.totalRecords || 0) /
        (response.data.meta?.limit || 10)
    ),
  };
};

// Get a single user by ID
export const getUserById = async (id: string): Promise<User> => {
  const response = await api.get(`/users/${id}`);
  return response.data;
};

// Create a new user
export const createUser = async (userData: Partial<User>): Promise<User> => {
  const response = await api.post('/users', userData);
  return response.data;
};

// Update an existing user
export const updateUser = async (
  id: string,
  userData: Partial<User>
): Promise<User> => {
  const response = await api.patch(`/users/${id}`, userData);
  return response.data;
};

// Delete a user
export const deleteUser = async (id: string): Promise<void> => {
  await api.delete(`/users/${id}`);
};
