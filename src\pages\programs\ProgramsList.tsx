import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getPrograms, deleteProgram, type Program } from "@/services/programService";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Eye, Plus, Edit, Trash2, Clock, Users, Dumbbell } from "lucide-react";
import ProgramModal from "@/components/programs/ProgramModal";

export default function ProgramsList() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingProgram, setEditingProgram] = useState<Program | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Fetch programs
  const {
    data: programs,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["programs"],
    queryFn: getPrograms,
  });

  // Filter programs based on search term
  const filteredPrograms = programs?.filter((program) =>
    program.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    program.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this program?")) return;
    
    setDeletingId(id);
    try {
      await deleteProgram(id);
      queryClient.invalidateQueries({ queryKey: ["programs"] });
    } catch (error) {
      console.error("Error deleting program:", error);
      alert("Failed to delete program");
    } finally {
      setDeletingId(null);
    }
  };

  const handleEdit = (program: Program) => {
    setEditingProgram(program);
    setIsModalOpen(true);
  };

  const handleAddNew = () => {
    setEditingProgram(null);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingProgram(null);
  };

  const handleFormSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["programs"] });
  };

  if (isLoading) {
    return (
      <DashboardLayout title="Programs">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">Loading programs...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (isError) {
    return (
      <DashboardLayout title="Programs">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600">Error loading programs: {error?.message}</p>
            <Button 
              onClick={() => queryClient.invalidateQueries({ queryKey: ["programs"] })}
              className="mt-4"
            >
              Try Again
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Programs">
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <h1 className="text-2xl font-bold">Programs</h1>
          <div className="flex items-center gap-2">
            <Button onClick={handleAddNew} className="w-full sm:w-auto">
              <Plus className="mr-2 h-4 w-4" />
              Add Program
            </Button>
          </div>
        </div>

        {/* Search and filters */}
        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search programs by name or type..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>



        {/* Programs Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredPrograms && filteredPrograms.length > 0 && filteredPrograms.map((program) => (
            <div key={program.id} className="rounded-lg border bg-card shadow-sm overflow-hidden">
              {/* Program Thumbnail */}
              <div className="aspect-video bg-gray-100 overflow-hidden">
                <img
                  src={program.thumbnail}
                  alt={program.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://via.placeholder.com/400x225?text=No+Image';
                  }}
                />
              </div>

              {/* Program Info */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-lg truncate">{program.name}</h3>
                  <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                    {program.type}
                  </span>
                </div>

                {/* Program Stats */}
                <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{program.duration} min</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Dumbbell className="h-4 w-4" />
                    <span>{program.exercises?.length || 0} exercises</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{program.subscriber?.length || 0} subscriber</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/dashboard/programs/${program.id}`)}
                    className="flex-1"
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(program)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(program.id!)}
                    disabled={deletingId === program.id}
                  >
                    {deletingId === program.id ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    ) : (
                      <Trash2 className="h-4 w-4 text-white" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {(!filteredPrograms || filteredPrograms.length === 0) && !isLoading && (
          <div className="text-center py-12">
            <Dumbbell className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No programs found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating a new program.'}
            </p>
            {!searchTerm && (
              <div className="mt-6">
                <Button onClick={handleAddNew}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Program
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Program Modal */}
        <ProgramModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          program={editingProgram || undefined}
          onSuccess={handleFormSuccess}
        />
      </div>
    </DashboardLayout>
  );
}
