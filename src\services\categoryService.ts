import {
  Timestamp,
  collection,
  addDoc,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Collections } from '@/constant';

// Define the Category interface
export interface Category {
  id?: string;
  name: string; // Stored in lowercase
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Get all categories
export const getCategories = async (): Promise<Category[]> => {
  try {
    const categoriesRef = collection(db, Collections.CATEGORIES);
    const q = query(categoriesRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);

    const categories: Category[] = [];
    querySnapshot.forEach((doc) => {
      categories.push({
        id: doc.id,
        ...doc.data()
      } as Category);
    });

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw new Error('Failed to fetch categories');
  }
};

// Get a single category by ID
export const getCategoryById = async (id: string): Promise<Category | null> => {
  try {
    const categoryRef = doc(db, Collections.CATEGORIES, id);
    const categorySnap = await getDoc(categoryRef);

    if (categorySnap.exists()) {
      return {
        id: categorySnap.id,
        ...categorySnap.data()
      } as Category;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error fetching category:', error);
    throw new Error('Failed to fetch category');
  }
};

// Check if a category name already exists
export const checkCategoryNameExists = async (name: string, excludeId?: string): Promise<boolean> => {
  try {
    const categoriesRef = collection(db, Collections.CATEGORIES);
    const normalizedName = name.toLowerCase().trim();
    const q = query(categoriesRef, where('name', '==', normalizedName));
    const querySnapshot = await getDocs(q);

    // If excludeId is provided, check if any other category has this name
    if (excludeId) {
      return querySnapshot.docs.some(doc => doc.id !== excludeId);
    }

    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking category name:', error);
    throw new Error('Failed to check category name');
  }
};

// Create a new category
export const createCategory = async (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<Category> => {
  try {
    // Normalize the name to lowercase
    const normalizedName = categoryData.name.toLowerCase().trim();
    
    // Check if category name already exists
    const nameExists = await checkCategoryNameExists(normalizedName);
    if (nameExists) {
      throw new Error('A category with this name already exists');
    }

    const now = Timestamp.now();
    const newCategoryData = {
      ...categoryData,
      name: normalizedName,
      createdAt: now,
      updatedAt: now,
    };

    const categoriesRef = collection(db, Collections.CATEGORIES);
    const docRef = await addDoc(categoriesRef, newCategoryData);

    return {
      id: docRef.id,
      ...newCategoryData,
    };
  } catch (error) {
    console.error('Error creating category:', error);
    // Re-throw the error with the original message if it's our custom validation error
    if (error instanceof Error && error.message === 'A category with this name already exists') {
      throw error;
    }
    throw new Error('Failed to create category');
  }
};

// Update an existing category
export const updateCategory = async (id: string, categoryData: Partial<Omit<Category, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> => {
  try {
    // Normalize the name to lowercase if provided
    const updateData: any = { ...categoryData };
    if (updateData.name) {
      updateData.name = updateData.name.toLowerCase().trim();
      
      // Check if category name already exists (excluding current category)
      const nameExists = await checkCategoryNameExists(updateData.name, id);
      if (nameExists) {
        throw new Error('A category with this name already exists');
      }
    }

    const categoryRef = doc(db, Collections.CATEGORIES, id);
    const finalUpdateData = {
      ...updateData,
      updatedAt: Timestamp.now(),
    };

    await updateDoc(categoryRef, finalUpdateData);
  } catch (error) {
    console.error('Error updating category:', error);
    // Re-throw the error with the original message if it's our custom validation error
    if (error instanceof Error && error.message === 'A category with this name already exists') {
      throw error;
    }
    throw new Error('Failed to update category');
  }
};

// Delete a category
export const deleteCategory = async (id: string): Promise<void> => {
  try {
    const categoryRef = doc(db, Collections.CATEGORIES, id);
    await deleteDoc(categoryRef);
  } catch (error) {
    console.error('Error deleting category:', error);
    throw new Error('Failed to delete category');
  }
};
