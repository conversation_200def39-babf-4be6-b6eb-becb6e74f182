interface PatientDetailsProps {
  patient?: {
    gender?: string;
    phoneNumber?: string;
    address?: string;
    isVerified?: boolean;
    isOnboarded?: boolean;
    createdAt?: string;
    updatedAt?: string;
  };
}

export default function PatientDetails({ patient }: PatientDetailsProps) {
  if (!patient) {
    return (
      <div className="text-sm text-muted-foreground">
        No patient information available
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="grid grid-cols-2 gap-2">
        <div className="text-sm font-medium">Gender</div>
        <div className="text-sm capitalize">
          {patient.gender || 'Not specified'}
        </div>
      </div>
      <div className="grid grid-cols-2 gap-2">
        <div className="text-sm font-medium">Phone</div>
        <div className="text-sm">{patient.phoneNumber || 'Not provided'}</div>
      </div>
      <div className="grid grid-cols-2 gap-2">
        <div className="text-sm font-medium">Address</div>
        <div className="text-sm">{patient.address || 'Not provided'}</div>
      </div>
      <div className="grid grid-cols-2 gap-2">
        <div className="text-sm font-medium">Verified</div>
        <div className="text-sm">{patient.isVerified ? 'Yes' : 'No'}</div>
      </div>
      <div className="grid grid-cols-2 gap-2">
        <div className="text-sm font-medium">Onboarded</div>
        <div className="text-sm">{patient.isOnboarded ? 'Yes' : 'No'}</div>
      </div>
    </div>
  );
}
