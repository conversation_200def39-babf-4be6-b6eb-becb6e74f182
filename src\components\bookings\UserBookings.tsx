import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  getBookings,
} from '@/services/bookingService';
import type { BookingFilterParams } from '@/services/bookingService';
import BookingFilters from './BookingFilters';
import BookingsList from './BookingsList';
import { Calendar } from 'lucide-react';

interface UserBookingsProps {
  userId: string;
  userType: 'doctor' | 'patient' | string;
}

export default function UserBookings({ userId, userType }: UserBookingsProps) {
  // Set up filters with the user ID
  const [filters, setFilters] = useState<BookingFilterParams>({
    page: 1,
    limit: 10,
    ...(userType === 'doctor' ? { doctor: userId } : { patient: userId }),
  });

  // Fetch bookings with the current filters
  const {
    data,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['bookings', filters],
    queryFn: () => getBookings(filters),
  });

  // Handle filter changes
  const handleFilterChange = (newFilters: BookingFilterParams) => {
    // Preserve the user ID filter
    setFilters({
      ...newFilters,
      ...(userType === 'doctor' ? { doctor: userId } : { patient: userId }),
    });
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setFilters((prev) => ({ ...prev, page: newPage }));
  };

  return (
    <div className="space-y-4">
      <div className="mb-4 flex items-center">
        <Calendar className="mr-2 h-5 w-5 text-primary" />
        <h3 className="text-lg font-medium">User Bookings</h3>
      </div>

      {/* Filters */}
      <BookingFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        showDoctorFilter={false}
        showPatientFilter={false}
      />

      {/* Bookings List */}
      <BookingsList
        bookings={data?.data || []}
        isLoading={isLoading}
        isError={isError}
        error={error as Error}
        page={filters.page || 1}
        totalPages={data?.totalPages || 1}
        onPageChange={handlePageChange}
        hidePatientInfo={userType === 'patient'}
        hideDoctorInfo={userType === 'doctor'}
      />
    </div>
  );
}
