import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  getUsers,
  type User,
  type UserFilterParams,
} from '@/services/userService';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import {
  Search,
  Trash2,
  Eye,
  Stethoscope,
  UserCheck,
  Clock,
  CheckCircle,
  BadgeCheck,
} from 'lucide-react';

interface UsersListProps {
  userType?: 'doctor' | 'patient';
}

export default function UsersList({ userType }: UsersListProps) {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<UserFilterParams>({
    page: 1,
    limit: 10,
    search: '',
    userType: userType || '',
    status: '',
  });

  // Update filters when userType prop changes
  useEffect(() => {
    if (userType) {
      setFilters((prev) => ({ ...prev, userType }));
    }
  }, [userType]);

  // Fetch users with the current filters
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['users', filters],
    queryFn: () => getUsers(filters),
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((prev) => ({ ...prev, search: e.target.value, page: 1 }));
  };

  // Handle filter changes
  const handleFilterChange = (name: string, value: string) => {
    setFilters((prev) => ({ ...prev, [name]: value, page: 1 }));
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setFilters((prev) => ({ ...prev, page: newPage }));
  };

  // Navigate to user details
  const handleViewUser = (userId: string) => {
    navigate(`/dashboard/users/${userId}`);
  };



  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get the appropriate title based on userType
  const getTitle = () => {
    if (userType === 'doctor') return 'Doctors';
    if (userType === 'patient') return 'Patients';

    return 'All Users';
  };

  return (
    <DashboardLayout title={getTitle()}>
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <h1 className="text-2xl font-bold">{getTitle()}</h1>
        </div>

        {/* Filters */}
        <div className="flex flex-col gap-4 md:flex-row">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              className="pl-9"
              value={filters.search}
              onChange={handleSearchChange}
            />
          </div>

          {!userType && (
            <select
              className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              value={filters.userType}
              onChange={(e) => handleFilterChange('userType', e.target.value)}
            >
              <option value="">All Types</option>
              <option value="patient">Patient</option>
              <option value="doctor">Doctor</option>
            </select>
          )}

          <select
            className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
          >
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>

        {/* Users table */}
        <div className="rounded-md border">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Name
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Email
                  </th>
                  {!userType && (
                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Type
                    </th>
                  )}
                  {userType === 'patient' && (
                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Gender
                    </th>
                  )}
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Verified
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Created
                  </th>
                  <th className="px-4 py-3 text-right text-sm font-medium">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td
                      colSpan={
                        userType === 'doctor'
                          ? 4
                          : userType === 'patient'
                          ? 5
                          : 6
                      }
                      className="px-4 py-3 text-center"
                    >
                      <div className="flex justify-center py-4">
                        <div className="h-6 w-6 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                      </div>
                    </td>
                  </tr>
                ) : isError ? (
                  <tr>
                    <td
                      colSpan={
                        userType === 'doctor'
                          ? 4
                          : userType === 'patient'
                          ? 5
                          : 6
                      }
                      className="px-4 py-3 text-center text-destructive"
                    >
                      Error loading users: {(error as Error).message}
                    </td>
                  </tr>
                ) : !data?.data || data.data.length === 0 ? (
                  <tr>
                    <td
                      colSpan={
                        userType === 'doctor'
                          ? 4
                          : userType === 'patient'
                          ? 5
                          : 6
                      }
                      className="px-4 py-3 text-center"
                    >
                      No users found
                    </td>
                  </tr>
                ) : (
                  data.data.map((user: User) => (
                    <tr key={user._id} className="border-b">
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          {user.userType === 'doctor' && (
                            <Stethoscope className="h-4 w-4 text-primary" />
                          )}
                          {user.userType === 'patient' && (
                            <UserCheck className="h-4 w-4 text-blue-500" />
                          )}
                          <span>{user.name}</span>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">{user.email}</td>
                      {!userType && (
                        <td className="px-4 py-3 text-sm capitalize">
                          {user.userType}
                        </td>
                      )}
                      {userType === 'patient' && (
                        <td className="px-4 py-3 text-sm capitalize">
                          {user.gender || 'Not specified'}
                        </td>
                      )}
                      <td className="px-7 py-3 text-sm">
                        <div className="flex items-center ">
                          {user.isVerified ? (
                            <div title="Verified">
                              <BadgeCheck className="h-5 w-5 text-green-600" />
                            </div>
                          ) : user.isOnboarded ? (
                            <div title="Onboarded but not verified">
                              <CheckCircle className="h-5 w-5 text-blue-600" />
                            </div>
                          ) : (
                            <div title="Not onboarded">
                              <Clock className="h-5 w-5 text-yellow-600" />
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {user.createdAt ? formatDate(user.createdAt) : 'N/A'}
                      </td>
                      <td className="px-4 py-3 text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewUser(user._id)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>

                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-destructive hover:bg-destructive/10 hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {data && data.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {(data.page - 1) * data.limit + 1} to{' '}
              {Math.min(data.page * data.limit, data.total)} of {data.total}{' '}
              users
            </p>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                disabled={data.page <= 1}
                onClick={() => handlePageChange(data.page - 1)}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={data.page >= data.totalPages}
                onClick={() => handlePageChange(data.page + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
