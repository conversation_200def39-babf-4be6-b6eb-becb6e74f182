import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  return (
    <header className={cn("bg-card border-b border-border", className)}>
      <div className="container flex h-16 items-center px-4">
        <Link to="/dashboard" className="flex items-center">
          <img 
            src="/ow_logo.png" 
            alt="OW Logo" 
            className="h-10 w-auto object-contain" 
          />
          <span className="ml-2 text-xl font-bold text-primary">Admin</span>
        </Link>
        <nav className="ml-auto flex items-center space-x-4">
          {/* Add your navigation items here */}
        </nav>
      </div>
    </header>
  );
}