import { type Program, getPrograms } from '@/services/programService';
import { assignProgramToPatient } from '@/services/assignedProgramService';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Loader2, AlertCircle, CheckCircle, User, Clock, Dumbbell } from 'lucide-react';

interface ProgramAssignmentFormProps {
  patientId: string;
  patientName: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function ProgramAssignmentForm({ 
  patientId, 
  patientName, 
  onSuccess, 
  onCancel 
}: ProgramAssignmentFormProps) {
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all programs
  const {
    data: programs,
    isLoading: programsLoading,
    isError: programsError,
  } = useQuery({
    queryKey: ["programs"],
    queryFn: getPrograms,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!selectedProgram) {
      setError('Please select a program to assign');
      return;
    }

    setIsLoading(true);

    try {
      await assignProgramToPatient(selectedProgram, patientId);
      onSuccess();
    } catch (err) {
      console.error('Error assigning program:', err);
      setError(err instanceof Error ? err.message : 'Failed to assign program');
    } finally {
      setIsLoading(false);
    }
  };

  if (programsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading programs...</span>
      </div>
    );
  }

  if (programsError) {
    return (
      <div className="flex items-center justify-center p-8 text-red-600">
        <AlertCircle className="h-8 w-8" />
        <span className="ml-2">Failed to load programs</span>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="flex items-center space-x-2 rounded-md border border-red-200 bg-red-50 p-3 text-red-700">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      {/* Patient Info */}
      <div className="rounded-lg border bg-blue-50 p-4">
        <div className="flex items-center space-x-2">
          <User className="h-5 w-5 text-blue-600" />
          <span className="font-medium text-blue-900">
            Assigning program to: {patientName}
          </span>
        </div>
      </div>

      {/* Program Selection */}
      <div className="space-y-3">
        <Label className="text-base font-medium">Select Program to Assign *</Label>
        <div className="rounded-md border p-3 max-h-96 overflow-y-auto">
          {programs && programs.length > 0 ? (
            <div className="space-y-3">
              {programs.map((program) => (
                <div 
                  key={program.id} 
                  className={`cursor-pointer rounded-lg border p-4 transition-all hover:shadow-md ${
                    selectedProgram?.id === program.id 
                      ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedProgram(program)}
                >
                  <div className="flex items-start space-x-3">
                    <input
                      type="radio"
                      id={`program-${program.id}`}
                      checked={selectedProgram?.id === program.id}
                      onChange={() => setSelectedProgram(program)}
                      className="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-medium text-gray-900">{program.name}</h4>
                        <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                          {program.type}
                        </span>
                      </div>
                      
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Dumbbell className="h-4 w-4" />
                          <span>{program.exercises?.length || 0} exercises</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{program.duration || 0} min</span>
                        </div>
                      </div>

                      {program.thumbnail && (
                        <div className="mt-2">
                          <img 
                            src={program.thumbnail} 
                            alt={program.name}
                            className="h-20 w-20 rounded-md object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500 text-center py-8">No programs available</p>
          )}
        </div>
      </div>

      {/* Selection Summary */}
      {selectedProgram && (
        <div className="rounded-lg border bg-green-50 p-4">
          <div className="flex items-start space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-900">Selected Program</h4>
              <p className="text-sm text-green-700">
                {selectedProgram.name} will be assigned to {patientName}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex flex-col-reverse sm:flex-row justify-end gap-2 sm:space-x-2 sm:gap-0">
        <Button type="button" variant="outline" onClick={onCancel} className="w-full sm:w-auto">
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={isLoading || !selectedProgram} 
          className="w-full sm:w-auto"
        >
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Assign Program
        </Button>
      </div>
    </form>
  );
}
