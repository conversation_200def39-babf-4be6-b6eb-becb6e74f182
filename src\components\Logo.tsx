import owLogo from '../../public/ow_logo.png';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function Logo({ className, size = 'md' }: LogoProps) {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12',
  };

  return (
    <img
      src={owLogo}
      alt="OW Logo"
      className={`w-auto object-contain ${sizeClasses[size]} ${className}`}
    />
  );
}
