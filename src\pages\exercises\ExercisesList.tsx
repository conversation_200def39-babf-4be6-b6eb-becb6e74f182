import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getExercises, deleteExercise } from "@/services/exerciseService";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Eye, Plus, Play, Edit, Trash2, Clock } from "lucide-react";
import ExerciseModal from "@/components/exercises/ExerciseModal";

export default function ExercisesList() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingExercise, setEditingExercise] = useState<any>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Fetch exercises
  const {
    data: exercises,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["exercises"],
    queryFn: getExercises,
  });

  // Filter exercises based on search term
  const filteredExercises = exercises?.filter((exercise) =>
    exercise.exerciseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    exercise.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this exercise?")) {
      return;
    }

    setDeletingId(id);
    try {
      await deleteExercise(id);
      queryClient.invalidateQueries({ queryKey: ["exercises"] });
    } catch (error) {
      console.error("Error deleting exercise:", error);
      alert("Failed to delete exercise. Please try again.");
    } finally {
      setDeletingId(null);
    }
  };

  const handleFormSuccess = () => {
    setIsModalOpen(false);
    setEditingExercise(null);
    queryClient.invalidateQueries({ queryKey: ["exercises"] });
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingExercise(null);
  };

  const handleAddExercise = () => {
    setEditingExercise(null);
    setIsModalOpen(true);
  };

  const handleEditExercise = (exercise: any) => {
    setEditingExercise(exercise);
    setIsModalOpen(true);
  };

  return (
    <DashboardLayout title="Exercises">
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <h1 className="text-2xl font-bold">Exercises</h1>
          <div className="flex items-center gap-2">
            <Button onClick={handleAddExercise} className="w-full sm:w-auto">
              <Plus className="mr-2 h-4 w-4" />
              Add Exercise
            </Button>
          </div>
        </div>

        {/* Search and filters */}
        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search exercises by name or type..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Exercise Modal */}
        <ExerciseModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          exercise={editingExercise}
          onSuccess={handleFormSuccess}
        />

        {/* Exercises List */}
        <div className="rounded-lg border bg-card shadow-sm">
          <div className="p-6">
            {isLoading ? (
              <div className="text-center">Loading exercises...</div>
            ) : isError ? (
              <div className="text-center text-red-500">
                Error loading exercises: {error instanceof Error ? error.message : "Unknown error"}
              </div>
            ) : filteredExercises && filteredExercises.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="px-4 py-3 text-left text-sm font-medium">Thumbnail</th>
                      <th className="px-4 py-3 text-left text-sm font-medium">Exercise Name</th>
                      <th className="px-4 py-3 text-left text-sm font-medium">Type</th>
                      <th className="px-4 py-3 text-left text-sm font-medium">Duration</th>
                      <th className="px-4 py-3 text-left text-sm font-medium">Created At</th>
                      <th className="px-4 py-3 text-right text-sm font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredExercises.map((exercise) => (
                      <tr key={exercise.id} className="border-b transition-colors hover:bg-muted/50">
                        <td className="px-4 py-3">
                          <img
                            src={exercise.thumbnail}
                            alt={exercise.exerciseName}
                            className="h-12 w-12 rounded-md object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzNkMzMC42Mjc0IDM2IDM2IDMwLjYyNzQgMzYgMjRDMzYgMTcuMzcyNiAzMC42Mjc0IDEyIDI0IDEyQzE3LjM3MjYgMTIgMTIgMTcuMzcyNiAxMiAyNEMxMiAzMC42Mjc0IDE3LjM3MjYgMzYgMjQgMzZaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNMjQgMjhDMjYuMjA5MSAyOCAyOCAyNi4yMDkxIDI4IDI0QzI4IDIxLjc5MDkgMjYuMjA5MSAyMCAyNCAyMEMyMS43OTA5IDIwIDIwIDIxLjc5MDkgMjAgMjRDMjAgMjYuMjA5MSAyMS43OTA5IDI4IDI0IDI4WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                            }}
                          />
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            <Play className="mr-2 h-4 w-4 text-primary" />
                            <span className="font-medium">{exercise.exerciseName}</span>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                            {exercise.type}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            <Clock className="mr-1 h-4 w-4 text-gray-500" />
                            <span>{exercise.duration} min</span>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          {exercise.createdAt && typeof exercise.createdAt.toDate === "function"
                            ? new Date(exercise.createdAt.toDate()).toLocaleDateString()
                            : "N/A"}
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => navigate(`/dashboard/exercises/${exercise.id}`)}
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEditExercise(exercise)}
                              title="Edit Exercise"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDelete(exercise.id!)}
                              disabled={deletingId === exercise.id}
                              title="Delete Exercise"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              {deletingId === exercise.id ? (
                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent" />
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center">
                {searchTerm ? "No exercises found matching your search." : "No exercises found."}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
