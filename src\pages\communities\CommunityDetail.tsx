import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getCommunityById } from '@/services/communityService';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, Users } from 'lucide-react';
import CommunityForm from '@/components/communities/CommunityForm';
import MembersList from '@/components/communities/MembersList';

export default function CommunityDetail() {
  const { communityId } = useParams<{ communityId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);

  // Fetch community details
  const {
    data: community,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["community", communityId],
    queryFn: () => getCommunityById(communityId!),
    enabled: !!communityId,
  });

  return (
    <DashboardLayout title="Community Details">
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" onClick={() => navigate("/dashboard/communities")}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold">
              {isLoading ? "Loading..." : community?.topicName || "Community Details"}
            </h1>
          </div>
          {!isEditing && community && (
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={() => setIsEditing(true)} disabled={isLoading}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          )}
        </div>

        {/* Main content */}
        {isLoading ? (
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <p className="text-center">Loading community details...</p>
          </div>
        ) : isError ? (
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <p className="text-center text-red-500">
              Error loading community: {error instanceof Error ? error.message : "Unknown error"}
            </p>
          </div>
        ) : community ? (
          <>
            {isEditing ? (
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h2 className="mb-4 text-xl font-semibold">Edit Community</h2>
                <CommunityForm
                  community={community}
                  onCancel={() => setIsEditing(false)}
                  onSuccess={() => {
                    setIsEditing(false);
                    queryClient.invalidateQueries({ queryKey: ['community', communityId] });
                    queryClient.invalidateQueries({ queryKey: ['communities'] });
                  }}
                />
              </div>
            ) : (
              <>
                <div className="rounded-lg border bg-card p-6 shadow-sm">
                  <h2 className="mb-4 text-lg font-medium">Community Information</h2>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Topic Name</p>
                      <p className="text-lg">{community.topicName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Created At</p>
                      <p>
                        {community.createdAt && typeof community.createdAt.toDate === "function"
                          ? new Date(community.createdAt.toDate()).toLocaleString()
                          : "N/A"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                      <p>
                        {community.updatedAt && typeof community.updatedAt.toDate === "function"
                          ? new Date(community.updatedAt.toDate()).toLocaleString()
                          : "N/A"}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="rounded-lg border bg-card p-6 shadow-sm">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-medium">Members</h2>
                    <div className="flex items-center space-x-2">
                      <Users className="h-5 w-5 text-muted-foreground" />
                      <span className="text-muted-foreground">{community.currentMembers?.length || 0} members</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <MembersList memberIds={community.currentMembers || []} />
                  </div>
                </div>
              </>
            )}
          </>
        ) : (
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <p className="text-center">Community not found.</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
