import { useQuery } from '@tanstack/react-query';
import { getAssignedPrograms, removeAssignedProgram } from '@/services/assignedProgramService';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, Calendar, Clock, Dumbbell, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface PatientAssignedProgramsProps {
  patientId: string;
}

export default function PatientAssignedPrograms({ patientId }: PatientAssignedProgramsProps) {
  const [removingProgramId, setRemovingProgramId] = useState<string | null>(null);

  const {
    data: assignedPrograms,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ['assignedPrograms', patientId],
    queryFn: () => getAssignedPrograms(patientId),
    enabled: !!patientId,
  });

  const handleRemoveProgram = async (assignedProgramId: string) => {
    if (!assignedProgramId) return;
    
    setRemovingProgramId(assignedProgramId);
    try {
      await removeAssignedProgram(assignedProgramId);
      refetch(); // Refresh the list
    } catch (err) {
      console.error('Failed to remove assigned program:', err);
    } finally {
      setRemovingProgramId(null);
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  // const getStatusIcon = (status: string) => {
  //   switch (status) {
  //     case 'active':
  //       return <Play className="h-4 w-4 text-green-600" />;
  //     case 'paused':
  //       return <Pause className="h-4 w-4 text-yellow-600" />;
  //     case 'completed':
  //       return <CheckCircle className="h-4 w-4 text-blue-600" />;
  //     default:
  //       return <Clock className="h-4 w-4 text-gray-600" />;
  //   }
  // };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading assigned programs...</span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8 text-red-600">
        <AlertCircle className="h-8 w-8" />
        <span className="ml-2">Failed to load assigned programs: {(error as Error).message}</span>
      </div>
    );
  }

  if (!assignedPrograms || assignedPrograms.length === 0) {
    return (
      <div className="text-center py-8">
        <Dumbbell className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">No Programs Assigned</h3>
        <p className="mt-2 text-sm text-gray-500">
          This patient doesn't have any programs assigned yet.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium flex items-center">
        <Dumbbell className="mr-2 h-5 w-5 text-primary" />
        Assigned Programs ({assignedPrograms.length})
      </h3>
      
      {/* Responsive Table View */}
      <div className="overflow-x-auto rounded-lg border">
        <table className="w-full border-collapse bg-card min-w-[600px]">
          <thead>
            <tr className="border-b bg-muted/50">
              <th className="text-left p-2 sm:p-4 font-medium text-xs sm:text-sm min-w-[180px]">Program</th>
              <th className="text-left p-2 sm:p-4 font-medium text-xs sm:text-sm min-w-[80px]">Type</th>
              <th className="text-left p-2 sm:p-4 font-medium text-xs sm:text-sm min-w-[80px]">Status</th>
              <th className="text-left p-2 sm:p-4 font-medium text-xs sm:text-sm min-w-[80px]">Exercises</th>
              <th className="text-left p-2 sm:p-4 font-medium text-xs sm:text-sm min-w-[80px]">Duration</th>
              <th className="text-left p-2 sm:p-4 font-medium text-xs sm:text-sm min-w-[100px]">Assigned Date</th>
              <th className="text-center p-2 sm:p-4 font-medium text-xs sm:text-sm min-w-[60px]">Actions</th>
            </tr>
          </thead>
          <tbody>
            {assignedPrograms.map((assignedProgram) => (
              <tr key={assignedProgram.id} className="border-b hover:bg-muted/25 transition-colors">
                <td className="p-2 sm:p-4">
                  <div className="flex items-center space-x-2 sm:space-x-3">
                    {assignedProgram.programData.thumbnail ? (
                      <img 
                        src={assignedProgram.programData.thumbnail} 
                        alt={assignedProgram.programData.name}
                        className="h-8 w-8 sm:h-12 sm:w-12 rounded-md object-cover flex-shrink-0"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="h-8 w-8 sm:h-12 sm:w-12 rounded-md bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <Dumbbell className="h-4 w-4 sm:h-6 sm:w-6 text-primary" />
                      </div>
                    )}
                    <div className="min-w-0 flex-1">
                      <h4 className="font-medium text-xs sm:text-sm">{assignedProgram.programData.name}</h4>
                    </div>
                  </div>
                </td>
                <td className="p-2 sm:p-4">
                  <span className="inline-flex items-center rounded-full bg-gray-100 px-2 sm:px-2.5 py-1 text-xs font-medium text-gray-800">
                    {assignedProgram.programData.type}
                  </span>
                </td>
                <td className="p-2 sm:p-4">
                  <div className="flex items-center space-x-1 sm:space-x-2">
                    {/* {getStatusIcon(assignedProgram.status)} */}
                    <span className={`inline-flex rounded-full px-1.5 sm:px-2 py-1 text-xs font-medium ${getStatusColor(assignedProgram.status)}`}>
                      {assignedProgram.status}
                    </span>
                  </div>
                </td>
                <td className="p-2 sm:p-4">
                  <div className="flex items-center space-x-1 text-xs sm:text-sm text-gray-600">
                    <Dumbbell className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span>{assignedProgram.programData.exercises?.length || 0}</span>
                  </div>
                </td>
                <td className="p-2 sm:p-4">
                  <div className="flex items-center space-x-1 text-xs sm:text-sm text-gray-600">
                    <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span>{assignedProgram.programData.duration || 0}m</span>
                  </div>
                </td>
                <td className="p-2 sm:p-4">
                  <div className="flex items-center space-x-1 text-xs sm:text-sm text-gray-600">
                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="whitespace-nowrap">{formatDate(assignedProgram.assignedAt)}</span>
                  </div>
                </td>
                <td className="p-2 sm:p-4 text-center">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleRemoveProgram(assignedProgram.id!)}
                    disabled={removingProgramId === assignedProgram.id}
                    className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                  >
                    {removingProgramId === assignedProgram.id ? (
                      <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                    )}
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
