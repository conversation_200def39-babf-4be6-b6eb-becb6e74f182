import { type Exercise } from '@/services/exerciseService';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import ExerciseForm from './ExerciseForm';

interface ExerciseModalProps {
  isOpen: boolean;
  onClose: () => void;
  exercise?: Exercise;
  onSuccess: () => void;
}

export default function ExerciseModal({ 
  isOpen, 
  onClose, 
  exercise, 
  onSuccess 
}: ExerciseModalProps) {
  const handleSuccess = () => {
    onSuccess();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {exercise ? 'Edit Exercise' : 'Add New Exercise'}
          </DialogTitle>
        </DialogHeader>
        <ExerciseForm
          exercise={exercise}
          onCancel={onClose}
          onSuccess={handleSuccess}
        />
      </DialogContent>
    </Dialog>
  );
}
