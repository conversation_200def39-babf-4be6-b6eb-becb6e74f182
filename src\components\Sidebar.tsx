import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import owLogo from '../../public/ow_logo.png';

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  return (
    <div
      className={cn(
        'flex h-screen w-64 flex-col border-r border-border bg-sidebar',
        className
      )}
    >
      <div className="flex h-16 items-center border-b border-sidebar-border px-6">
        <Link to="/dashboard" className="flex items-center">
          <img
            src={owLogo}
            alt="OW Logo"
            className="h-8 w-auto object-contain"
          />
          <span className="ml-2 text-lg font-semibold text-sidebar-foreground">
            Admin
          </span>
        </Link>
      </div>
      {/* Sidebar navigation items */}
    </div>
  );
}
