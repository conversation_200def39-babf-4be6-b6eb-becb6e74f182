import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

export default function Unauthorized() {
  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background p-4">
      <div className="w-full max-w-md space-y-6 rounded-lg border bg-card p-8 shadow-lg">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-destructive">Access Denied</h1>
          <p className="mt-4 text-muted-foreground">
            You don't have permission to access the admin dashboard.
          </p>
          <p className="mt-2 text-muted-foreground">
            Only users with admin privileges can access this area.
          </p>
        </div>
        
        <Button 
          onClick={handleLogout} 
          className="w-full"
          variant="default"
        >
          Return to Login
        </Button>
      </div>
    </div>
  );
}
