import { create } from 'zustand';
import { getUserById, type User } from '@/services/userService';

interface UsersState {
  // Store users by their ID for quick lookup
  users: Record<string, User>;
  // Track loading states for individual users
  loadingUsers: Set<string>;
  // Track if we're loading multiple users
  isLoading: boolean;
  // Error state
  error: string | null;
}

interface UsersActions {
  // Fetch a single user by ID
  fetchUser: (userId: string) => Promise<User | null>;
  // Fetch multiple users by IDs
  fetchUsers: (userIds: string[]) => Promise<User[]>;
  // Get user from store (returns cached user if available)
  getUser: (userId: string) => User | null;
  // Get multiple users from store
  getUsers: (userIds: string[]) => (User | null)[];
  // Get user names from member IDs
  getUserNames: (memberIds: string[]) => Promise<{ id: string; name: string }[]>;
  // Clear error
  clearError: () => void;
  // Reset store
  reset: () => void;
}

type UsersStore = UsersState & UsersActions;

const initialState: UsersState = {
  users: {},
  loadingUsers: new Set(),
  isLoading: false,
  error: null,
};

export const useUsersStore = create<UsersStore>((set, get) => ({
  ...initialState,

  fetchUser: async (userId: string) => {
    const state = get();
    
    // Return cached user if available
    if (state.users[userId]) {
      return state.users[userId];
    }

    // Don't fetch if already loading
    if (state.loadingUsers.has(userId)) {
      return null;
    }

    try {
      set((state) => ({
        loadingUsers: new Set([...state.loadingUsers, userId]),
        error: null,
      }));

      const user = await getUserById(userId);
      
      set((state) => ({
        users: { ...state.users, [userId]: user },
        loadingUsers: new Set([...state.loadingUsers].filter(id => id !== userId)),
      }));

      return user;
    } catch (error) {
      set((state) => ({
        loadingUsers: new Set([...state.loadingUsers].filter(id => id !== userId)),
        error: error instanceof Error ? error.message : 'Failed to fetch user',
      }));
      return null;
    }
  },

  fetchUsers: async (userIds: string[]) => {
    const state = get();
    
    // Filter out users that are already cached or being loaded
    const uncachedUserIds = userIds.filter(
      id => !state.users[id] && !state.loadingUsers.has(id)
    );

    if (uncachedUserIds.length === 0) {
      // Return cached users
      return userIds.map(id => state.users[id]).filter(Boolean);
    }

    try {
      set((state) => ({
        isLoading: true,
        loadingUsers: new Set([...state.loadingUsers, ...uncachedUserIds]),
        error: null,
      }));

      // Fetch users in parallel
      const userPromises = uncachedUserIds.map(id => getUserById(id));
      const users = await Promise.allSettled(userPromises);

      const newUsers: Record<string, User> = {};
      users.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const userId = uncachedUserIds[index];
          newUsers[userId] = result.value;
        }
      });

      set((state) => ({
        users: { ...state.users, ...newUsers },
        loadingUsers: new Set([...state.loadingUsers].filter(id => !uncachedUserIds.includes(id))),
        isLoading: false,
      }));

      // Return all requested users (cached + newly fetched)
      return userIds.map(id => get().users[id]).filter(Boolean);
    } catch (error) {
      set((state) => ({
        loadingUsers: new Set([...state.loadingUsers].filter(id => !uncachedUserIds.includes(id))),
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch users',
      }));
      return [];
    }
  },

  getUser: (userId: string) => {
    return get().users[userId] || null;
  },

  getUsers: (userIds: string[]) => {
    const state = get();
    return userIds.map(id => state.users[id] || null);
  },

  getUserNames: async (memberIds: string[]) => {
    const { fetchUsers } = get();
    
    try {
      const users = await fetchUsers(memberIds);
      return users.map(user => ({
        id: user._id,
        name: user.name,
      }));
    } catch (error) {
      console.error('Failed to get user names:', error);
      return memberIds.map(id => ({ id, name: id })); // Fallback to IDs
    }
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set(initialState);
  },
}));
