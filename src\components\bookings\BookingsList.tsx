import type { Booking } from '@/services/bookingService';
import {
  Calendar,
  Clock,
  DollarSign,
  CheckCircle,
  Star,
  User,
  Stethoscope,
} from 'lucide-react';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface BookingsListProps {
  bookings: Booking[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  page: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  hidePatientInfo?: boolean;
  hideDoctorInfo?: boolean;
}

export default function BookingsList({
  bookings,
  isLoading,
  isError,
  error,
  page,
  totalPages,
  onPageChange,
  hidePatientInfo = false,
  hideDoctorInfo = false,
}: BookingsListProps) {
  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Format time for display
  const formatTime = (timeString: string) => {
    // Convert 24-hour format to 12-hour format
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Service
                </th>
                {!hidePatientInfo && (
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Patient
                  </th>
                )}
                {!hideDoctorInfo && (
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Doctor
                  </th>
                )}
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Date & Time
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Price
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Rating
                </th>
              </tr>
            </thead>
            <tbody>
              {isLoading ? (
                <tr>
                  <td
                    colSpan={7}
                    className="px-4 py-3 text-center"
                  >
                    <div className="flex justify-center py-4">
                      <div className="h-6 w-6 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                    </div>
                  </td>
                </tr>
              ) : isError ? (
                <tr>
                  <td
                    colSpan={7}
                    className="px-4 py-3 text-center text-red-500"
                  >
                    Error loading bookings: {error?.message || 'Unknown error'}
                  </td>
                </tr>
              ) : bookings.length === 0 ? (
                <tr>
                  <td
                    colSpan={7}
                    className="px-4 py-3 text-center"
                  >
                    No bookings found
                  </td>
                </tr>
              ) : (
                bookings.map((booking) => (
                  <tr key={booking.id} className="border-b">
                    <td className="px-4 py-3 text-sm">
                      <div className="font-medium">{booking.service.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {booking.service.sessionTime} min session
                      </div>
                    </td>
                    {!hidePatientInfo && (
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-blue-500" />
                          <span>{booking.patient.name}</span>
                        </div>
                      </td>
                    )}
                    {!hideDoctorInfo && (
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <Stethoscope className="h-4 w-4 text-primary" />
                          <span>{booking.doctor.name}</span>
                        </div>
                      </td>
                    )}
                    <td className="px-4 py-3 text-sm">
                      <div className="flex flex-col">
                        <div className="flex items-center">
                          <Calendar className="mr-1 h-4 w-4 text-muted-foreground" />
                          {formatDate(booking.bookingDateTime.date)}
                        </div>
                        <div className="flex items-center">
                          <Clock className="mr-1 h-4 w-4 text-muted-foreground" />
                          {formatTime(booking.bookingDateTime.time)}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 text-green-500" />
                        {booking.service.price}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                          booking.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}
                      >
                        {booking.status === 'completed' && (
                          <CheckCircle className="mr-1 h-3 w-3" />
                        )}
                        {booking.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {booking.review ? (
                        <div className="flex items-center">
                          <Star
                            className="mr-1 h-4 w-4 text-yellow-500"
                            fill="currentColor"
                          />
                          {booking.review.rating}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">No review</span>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => onPageChange(Math.max(1, page - 1))}
                disabled={page === 1}
                className={page === 1 ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(
              (pageNum) => (
                <PaginationItem key={pageNum}>
                  <PaginationLink
                    onClick={() => onPageChange(pageNum)}
                    isActive={pageNum === page}
                  >
                    {pageNum}
                  </PaginationLink>
                </PaginationItem>
              )
            )}
            <PaginationItem>
              <PaginationNext
                onClick={() => onPageChange(Math.min(totalPages, page + 1))}
                disabled={page === totalPages}
                className={
                  page === totalPages ? 'pointer-events-none opacity-50' : ''
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
