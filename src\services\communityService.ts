import {
  Timestamp,
  collection,
  addDoc,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Collections } from '@/constant';

// Define the Community interface
export interface Community {
  id?: string;
  topicName: string;
  currentMembers: string[]; // Array of user IDs
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Get all communities
export const getCommunities = async (): Promise<Community[]> => {
  try {
    const communitiesRef = collection(db, Collections.COMMUNITIES);
    const q = query(communitiesRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);

    const communities: Community[] = [];
    querySnapshot.forEach((doc) => {
      communities.push({
        id: doc.id,
        ...doc.data()
      } as Community);
    });

    return communities;
  } catch (error) {
    console.error('Error fetching communities:', error);
    throw new Error('Failed to fetch communities');
  }
};

// Get a single community by ID
export const getCommunityById = async (id: string): Promise<Community | null> => {
  try {
    const communityRef = doc(db, Collections.COMMUNITIES, id);
    const communitySnap = await getDoc(communityRef);

    if (communitySnap.exists()) {
      return {
        id: communitySnap.id,
        ...communitySnap.data()
      } as Community;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error fetching community:', error);
    throw new Error('Failed to fetch community');
  }
};

// Check if a topic name already exists
export const checkTopicNameExists = async (topicName: string, excludeId?: string): Promise<boolean> => {
  try {
    const communitiesRef = collection(db, Collections.COMMUNITIES);
    const q = query(
      communitiesRef,
      where('topicName', '==', topicName.trim())
    );
    const querySnapshot = await getDocs(q);

    // If excludeId is provided (for updates), check if any other document has this topic name
    if (excludeId) {
      return querySnapshot.docs.some(doc => doc.id !== excludeId);
    }

    // For new communities, any existing document with this topic name is a duplicate
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking topic name:', error);
    throw new Error('Failed to check topic name');
  }
};

// Create a new community
export const createCommunity = async (communityData: Omit<Community, 'id' | 'createdAt' | 'updatedAt'>): Promise<Community> => {
  try {
    // Check if topic name already exists
    const topicExists = await checkTopicNameExists(communityData.topicName);
    if (topicExists) {
      throw new Error('A community with this topic name already exists');
    }

    const now = Timestamp.now();
    const newCommunityData = {
      ...communityData,
      createdAt: now,
      updatedAt: now,
    };

    const communitiesRef = collection(db, Collections.COMMUNITIES);
    const docRef = await addDoc(communitiesRef, newCommunityData);

    return {
      id: docRef.id,
      ...newCommunityData,
    };
  } catch (error) {
    console.error('Error creating community:', error);
    // Re-throw the error with the original message if it's our custom validation error
    if (error instanceof Error && error.message === 'A community with this topic name already exists') {
      throw error;
    }
    throw new Error('Failed to create community');
  }
};

// Update an existing community
export const updateCommunity = async (id: string, communityData: Partial<Omit<Community, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> => {
  try {
    // Check if topic name already exists (excluding current community)
    if (communityData.topicName) {
      const topicExists = await checkTopicNameExists(communityData.topicName, id);
      if (topicExists) {
        throw new Error('A community with this topic name already exists');
      }
    }

    const communityRef = doc(db, Collections.COMMUNITIES, id);
    const updateData = {
      ...communityData,
      updatedAt: Timestamp.now(),
    };

    await updateDoc(communityRef, updateData);
  } catch (error) {
    console.error('Error updating community:', error);
    // Re-throw the error with the original message if it's our custom validation error
    if (error instanceof Error && error.message === 'A community with this topic name already exists') {
      throw error;
    }
    throw new Error('Failed to update community');
  }
};

// Delete a community
export const deleteCommunity = async (id: string): Promise<void> => {
  try {
    const communityRef = doc(db, Collections.COMMUNITIES, id);
    await deleteDoc(communityRef);
  } catch (error) {
    console.error('Error deleting community:', error);
    throw new Error('Failed to delete community');
  }
};
