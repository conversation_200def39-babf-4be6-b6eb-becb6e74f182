import { type Exercise, createExercise, updateExercise, checkExerciseNameExists } from '@/services/exerciseService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FileUpload } from '@/components/ui/file-upload';
import { uploadThumbnail, uploadVideo, getVideoDuration, type UploadResult } from '@/services/fileUploadService';
import { useState, useEffect, useCallback } from 'react';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';

interface ExerciseFormProps {
  exercise?: Exercise;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function ExerciseForm({ exercise, onSuccess, onCancel }: ExerciseFormProps) {
  const [exerciseName, setExerciseName] = useState(exercise?.exerciseName || '');
  const [thumbnail, setThumbnail] = useState(exercise?.thumbnail || '');
  const [type, setType] = useState(exercise?.type || '');
  const [videoUrl, setVideoUrl] = useState(exercise?.videoUrl || '');
  const [duration, setDuration] = useState(exercise?.duration?.toString() || '');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCheckingDuplicate, setIsCheckingDuplicate] = useState(false);
  const [isDuplicate, setIsDuplicate] = useState(false);
  const [isValidName, setIsValidName] = useState(false);

  // File upload states
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [isUploadingThumbnail, setIsUploadingThumbnail] = useState(false);
  const [isUploadingVideo, setIsUploadingVideo] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);



  // File upload handlers
  const handleThumbnailFileSelect = async (file: File) => {
    setThumbnailFile(file);
    setIsUploadingThumbnail(true);
    setUploadError(null);

    try {
      const result: UploadResult = await uploadThumbnail(file, exerciseName);
      setThumbnail(result.url);
    } catch (err) {
      setUploadError(err instanceof Error ? err.message : 'Failed to upload thumbnail');
      setThumbnailFile(null);
    } finally {
      setIsUploadingThumbnail(false);
    }
  };

  const handleVideoFileSelect = async (file: File) => {
    setVideoFile(file);
    setIsUploadingVideo(true);
    setUploadError(null);

    try {
      // Get video duration before uploading
      const videoDuration = await getVideoDuration(file);
      setDuration(videoDuration.toString());

      const result: UploadResult = await uploadVideo(file, exerciseName);
      setVideoUrl(result.url);
    } catch (err) {
      setUploadError(err instanceof Error ? err.message : 'Failed to upload video');
      setVideoFile(null);
      setDuration(''); // Clear duration if video upload fails
    } finally {
      setIsUploadingVideo(false);
    }
  };

  const handleThumbnailRemove = () => {
    setThumbnailFile(null);
    setThumbnail('');
  };

  const handleVideoRemove = () => {
    setVideoFile(null);
    setVideoUrl('');
    // Clear duration when removing video file, allow manual input
    if (videoFile) {
      setDuration('');
    }
  };

  // Debounced function to check for duplicate exercise names
  const checkDuplicate = useCallback(async (name: string) => {
    if (!name.trim() || name.length < 2) {
      setIsDuplicate(false);
      setIsValidName(false);
      return;
    }

    setIsCheckingDuplicate(true);
    try {
      const exists = await checkExerciseNameExists(name.trim(), exercise?.id);
      setIsDuplicate(exists);
      setIsValidName(!exists && name.trim().length > 0);
    } catch (error) {
      console.error('Error checking duplicate:', error);
      setIsDuplicate(false);
      setIsValidName(false);
    } finally {
      setIsCheckingDuplicate(false);
    }
  }, [exercise?.id]);

  // Debounce the duplicate check
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (exerciseName.trim()) {
        checkDuplicate(exerciseName);
      } else {
        setIsDuplicate(false);
        setIsValidName(false);
        setIsCheckingDuplicate(false);
      }
    }, 500); // 500ms delay

    return () => clearTimeout(timeoutId);
  }, [exerciseName, checkDuplicate]);

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!exerciseName.trim()) {
      setError('Exercise name is required');
      return;
    }

    if (!thumbnail.trim()) {
      setError('Thumbnail is required');
      return;
    }

    if (!type.trim()) {
      setError('Exercise type is required');
      return;
    }

    if (!videoUrl.trim()) {
      setError('Video is required');
      return;
    }

    // Duration validation: only required if video file is uploaded (auto-extracted)
    // For URL videos, duration is optional and can be 0
    if (videoFile && (!duration.trim() || isNaN(Number(duration)) || Number(duration) <= 0)) {
      setError('Failed to extract video duration. Please try uploading the video again.');
      return;
    }

    // Only validate URLs if they're not from file uploads
    if (!thumbnailFile && !validateUrl(thumbnail)) {
      setError('Please enter a valid thumbnail URL or upload a file');
      return;
    }

    if (!videoFile && !validateUrl(videoUrl)) {
      setError('Please enter a valid video URL or upload a file');
      return;
    }

    if (isDuplicate) {
      setError('An exercise with this name already exists');
      return;
    }

    if (exerciseName.trim().length < 2) {
      setError('Exercise name must be at least 2 characters long');
      return;
    }

    if (isUploadingThumbnail || isUploadingVideo) {
      setError('Please wait for file uploads to complete');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (exercise?.id) {
        // Update existing exercise
        const updateData = {
          exerciseName: exerciseName.trim(),
          thumbnail: thumbnail.trim(),
          type: type.trim(),
          videoUrl: videoUrl.trim(),
          duration: duration ? Number(duration) : 0, // Use extracted duration or 0 for URL videos
        };

        await updateExercise(exercise.id, updateData);
      } else {
        // Create new exercise
        const newExerciseData = {
          exerciseName: exerciseName.trim(),
          thumbnail: thumbnail.trim(),
          type: type.trim(),
          videoUrl: videoUrl.trim(),
          duration: duration ? Number(duration) : 0, // Use extracted duration or 0 for URL videos
        };

        await createExercise(newExerciseData);
      }

      onSuccess();
    } catch (err) {
      console.error(`Error ${exercise?.id ? 'updating' : 'creating'} exercise:`, err);
      setError(err instanceof Error ? err.message : `Failed to ${exercise?.id ? 'update' : 'create'} exercise`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Exercise Name */}
          <div className="md:col-span-2">
            <label htmlFor="exerciseName" className="block text-sm font-medium text-gray-700">
              Exercise Name *
            </label>
            <div className="relative">
              <Input
                type="text"
                id="exerciseName"
                value={exerciseName}
                onChange={(e) => {
                  setExerciseName(e.target.value);
                  if (error) setError(null);
                }}
                required
                className={`mt-1 pr-10 ${
                  isDuplicate ? 'border-red-500 focus:border-red-500' :
                  isValidName ? 'border-green-500 focus:border-green-500' : ''
                }`}
                placeholder="Enter exercise name"
                disabled={isLoading}
                minLength={2}
              />

              {/* Validation icons */}
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {isCheckingDuplicate ? (
                  <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                ) : isDuplicate ? (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                ) : isValidName ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />  
                ) : null}
              </div>
            </div>

            {/* Real-time validation messages */}
            {exerciseName.trim() && !isCheckingDuplicate && (
              <div className="mt-1">
                {isDuplicate ? (
                  <p className="text-xs text-red-600">This exercise name is already taken</p>
                ) : isValidName ? (
                  <p className="text-xs text-green-600">Exercise name is available</p>
                ) : exerciseName.trim().length < 2 ? (
                  <p className="text-xs text-gray-500">Exercise name must be at least 2 characters</p>
                ) : null}
              </div>
            )}
          </div>

          {/* Exercise Type */}
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700">
              Exercise Type *
            </label>
            <Input
              id="type"
              type="text"
              value={type}
              onChange={(e) => {
                setType(e.target.value);
                if (error) setError(null);
              }}
              required
              className="mt-1"
              placeholder="Enter exercise type (e.g., Cardio, Strength, Yoga)"
              disabled={isLoading}
              minLength={2}
            />
          </div>

          {/* Duration - Only shown when video file is uploaded */}
          {videoFile && (
            <div>
              <label htmlFor="duration" className="block text-sm font-medium text-gray-700">
                Duration (minutes)
              </label>
              <Input
                type="number"
                id="duration"
                value={duration}
                readOnly
                disabled
                className="mt-1 bg-gray-50"
                placeholder="Automatically detected"
              />
              <p className="mt-1 text-xs text-gray-500">
                Duration automatically detected from uploaded video
              </p>
            </div>
          )}

          {/* Thumbnail Upload */}
          <div className="md:col-span-2">
            <FileUpload
              onFileSelect={handleThumbnailFileSelect}
              onFileRemove={handleThumbnailRemove}
              onUrlChange={(url) => {
                setThumbnail(url);
                if (error) setError(null);
              }}
              accept="image/*"
              fileType="image"
              currentFile={thumbnailFile}
              currentUrl={thumbnail}
              isUploading={isUploadingThumbnail}
              error={uploadError}
              disabled={isLoading}
              label="Thumbnail"
              description="Upload an image file or enter a URL"
              required
              allowUrlInput
            />
          </div>

          {/* Video Upload */}
          <div className="md:col-span-2">
            <FileUpload
              onFileSelect={handleVideoFileSelect}
              onFileRemove={handleVideoRemove}
              onUrlChange={(url) => {
                setVideoUrl(url);
                // Clear video file when URL is entered manually
                if (videoFile) {
                  setVideoFile(null);
                  setDuration(''); // Clear duration when switching to URL
                }
                if (error) setError(null);
              }}
              accept="video/*"
              fileType="video"
              currentFile={videoFile}
              currentUrl={videoUrl}
              isUploading={isUploadingVideo}
              error={uploadError}
              disabled={isLoading}
              label="Exercise Video"
              description="Upload a video file or enter a URL"
              required
              allowUrlInput
            />
          </div>
        </div>

        {(error || uploadError) && (
          <div className="flex items-center gap-2 text-sm text-red-600 mt-4">
            <AlertCircle className="h-4 w-4" />
            <span>{error || uploadError}</span>
          </div>
        )}

        <div className="flex justify-end space-x-2 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              isLoading ||
              isDuplicate ||
              !exerciseName.trim() ||
              exerciseName.trim().length < 2 ||
              isCheckingDuplicate ||
              !thumbnail.trim() ||
              !type.trim() ||
              !videoUrl.trim() ||
              isUploadingThumbnail ||
              isUploadingVideo ||
              // For uploaded videos, ensure duration was extracted
              (videoFile && (!duration.trim() || isNaN(Number(duration)) || Number(duration) <= 0))
            }
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {exercise ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              exercise ? 'Update Exercise' : 'Create Exercise'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
