import api from '@/lib/api';

// Types
export interface Booking {
  id: string;
  patient: {
    _id: string;
    name: string;
    email: string;
    photo?: string;
    phoneNumber?: string;
  };
  doctor: {
    _id: string;
    name: string;
    email: string;
    photo?: string;
    phoneNumber?: string;
  };
  service: {
    name: string;
    sessionTime: number;
    price: string;
    description: string;
  };
  bookingDateTime: {
    date: string;
    time: string;
    day: string;
  };
  status: 'active' | 'completed';
  payment?: {
    payment_id: string;
    paidAt: Date;
    amount: string;
  };
  review?: {
    rating: number;
    review: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface BookingFilterParams {
  page?: number;
  limit?: number;
  status?: 'active' | 'completed';
  date?: string;
  doctor?: string;
  patient?: string;
  isPast?: boolean;
}

export interface PaginatedBookingsResponse {
  data: Booking[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Get all bookings with pagination and filtering
export const getBookings = async (
  params: BookingFilterParams = {}
): Promise<PaginatedBookingsResponse> => {
  const response = await api.get('/bookings', { params });
  
  return {
    data: response.data.results || [],
    total: response.data.meta?.totalSize || 0,
    page: response.data.meta?.page || 1,
    limit: response.data.meta?.limit || 10,
    totalPages: Math.ceil(
      (response.data.meta?.totalSize || 0) / (response.data.meta?.limit || 10)
    ),
  };
};

// Get a single booking by ID
export const getBookingById = async (id: string): Promise<Booking> => {
  const response = await api.get(`/bookings/${id}`);
  return response.data;
};

// Mark a booking as completed
export const markBookingAsCompleted = async (id: string): Promise<Booking> => {
  const response = await api.patch(`/bookings/mark-as-completed/${id}`);
  return response.data;
};

// Delete a booking
export const deleteBooking = async (id: string): Promise<void> => {
  await api.delete(`/bookings/${id}`);
};
