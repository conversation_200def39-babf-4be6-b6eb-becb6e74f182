import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '@/lib/firebase';

export interface UploadResult {
  url: string;
  path: string;
}

export interface UploadProgress {
  progress: number;
  isUploading: boolean;
  error?: string;
}

/**
 * Upload a file to Firebase Storage
 * @param file - The file to upload
 * @param folder - The folder path in storage (e.g., 'exercises/thumbnails')
 * @param fileName - Optional custom filename, if not provided, uses original filename with timestamp
 * @returns Promise with download URL and storage path
 */
export const uploadFile = async (
  file: File,
  folder: string,
  fileName?: string
): Promise<UploadResult> => {
  try {
    // Validate file
    if (!file) {
      throw new Error('No file provided');
    }

    // Generate filename if not provided
    const timestamp = Date.now();
    // const fileExtension = file.name.split('.').pop();
    const finalFileName = fileName || `${timestamp}_${file.name}`;
    
    // Create storage reference
    const storageRef = ref(storage, `${folder}/${finalFileName}`);
    
    // Upload file
    const snapshot = await uploadBytes(storageRef, file);
    
    // Get download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return {
      url: downloadURL,
      path: snapshot.ref.fullPath
    };
  } catch (error) {
    console.error('Error uploading file:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to upload file');
  }
};

/**
 * Upload thumbnail image for exercises
 * @param file - Image file to upload
 * @param exerciseName - Name of the exercise for filename
 * @returns Promise with download URL and storage path
 */
export const uploadThumbnail = async (file: File, exerciseName?: string): Promise<UploadResult> => {
  // Validate file type
  if (!file.type.startsWith('image/')) {
    throw new Error('Please select a valid image file');
  }

  // Validate file size (max 5MB)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    throw new Error('Image file size must be less than 5MB');
  }

  const fileName = exerciseName 
    ? `${exerciseName.toLowerCase().replace(/\s+/g, '_')}_thumbnail_${Date.now()}.${file.name.split('.').pop()}`
    : undefined;

  return uploadFile(file, 'exercises/thumbnails', fileName);
};

/**
 * Upload video file for exercises
 * @param file - Video file to upload
 * @param exerciseName - Name of the exercise for filename
 * @returns Promise with download URL and storage path
 */
export const uploadVideo = async (file: File, exerciseName?: string): Promise<UploadResult> => {
  // Validate file type
  const allowedVideoTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'];
  if (!allowedVideoTypes.includes(file.type)) {
    throw new Error('Please select a valid video file (MP4, WebM, OGG, AVI, MOV)');
  }

  // Validate file size (max 100MB)
  const maxSize = 100 * 1024 * 1024; // 100MB
  if (file.size > maxSize) {
    throw new Error('Video file size must be less than 100MB');
  }

  const fileName = exerciseName 
    ? `${exerciseName.toLowerCase().replace(/\s+/g, '_')}_video_${Date.now()}.${file.name.split('.').pop()}`
    : undefined;

  return uploadFile(file, 'exercises/videos', fileName);
};

/**
 * Delete a file from Firebase Storage
 * @param path - The full path of the file in storage
 */
export const deleteFile = async (path: string): Promise<void> => {
  try {
    const fileRef = ref(storage, path);
    await deleteObject(fileRef);
  } catch (error) {
    console.error('Error deleting file:', error);
    throw new Error('Failed to delete file');
  }
};

/**
 * Get file size in human readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size string
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get video duration in minutes from a video file
 * @param file - Video file
 * @returns Promise with duration in minutes (rounded to 1 decimal place)
 */
export const getVideoDuration = (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.preload = 'metadata';

    video.onloadedmetadata = () => {
      window.URL.revokeObjectURL(video.src);
      const durationInMinutes = Math.round((video.duration / 60) * 10) / 10; // Round to 1 decimal place
      resolve(durationInMinutes);
    };

    video.onerror = () => {
      window.URL.revokeObjectURL(video.src);
      reject(new Error('Failed to load video metadata'));
    };

    video.src = URL.createObjectURL(file);
  });
};

/**
 * Validate file type and size
 * @param file - File to validate
 * @param type - 'image' or 'video'
 * @returns Validation result
 */
export const validateFile = (file: File, type: 'image' | 'video'): { isValid: boolean; error?: string } => {
  if (!file) {
    return { isValid: false, error: 'No file selected' };
  }

  if (type === 'image') {
    if (!file.type.startsWith('image/')) {
      return { isValid: false, error: 'Please select a valid image file' };
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return { isValid: false, error: 'Image file size must be less than 5MB' };
    }
  } else if (type === 'video') {
    const allowedVideoTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'];
    if (!allowedVideoTypes.includes(file.type)) {
      return { isValid: false, error: 'Please select a valid video file (MP4, WebM, OGG, AVI, MOV)' };
    }

    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      return { isValid: false, error: 'Video file size must be less than 100MB' };
    }
  }

  return { isValid: true };
};
