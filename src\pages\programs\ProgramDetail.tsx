import { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getProgramWithExercises, deleteProgram } from "@/services/programService";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, Trash2, Clock, Users, Dumbbell, Play, ExternalLink } from "lucide-react";
import ProgramForm from "@/components/programs/ProgramForm";

export default function ProgramDetail() {
  const { programId } = useParams<{ programId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch program details with exercises
  const {
    data: program,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["program", programId],
    queryFn: () => getProgramWithExercises(programId!),
    enabled: !!programId,
  });

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this program? This action cannot be undone.")) {
      return;
    }

    setIsDeleting(true);
    try {
      await deleteProgram(programId!);
      queryClient.invalidateQueries({ queryKey: ["programs"] });
      navigate("/dashboard/programs");
    } catch (error) {
      console.error("Error deleting program:", error);
      alert("Failed to delete program");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleEditSuccess = () => {
    setIsEditing(false);
    queryClient.invalidateQueries({ queryKey: ["program", programId] });
    queryClient.invalidateQueries({ queryKey: ["programs"] });
  };

  if (isLoading) {
    return (
      <DashboardLayout title="Loading...">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">Loading program details...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (isError || !program) {
    return (
      <DashboardLayout title="Error">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600">
              {error?.message || "Program not found"}
            </p>
            <Button 
              onClick={() => navigate("/dashboard/programs")}
              className="mt-4"
            >
              Back to Programs
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (isEditing) {
    return (
      <DashboardLayout title="Edit Program">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsEditing(false)}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">Edit Program</h1>
          </div>
          
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <ProgramForm
              program={program}
              onCancel={() => setIsEditing(false)}
              onSuccess={handleEditSuccess}
            />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title={program.name}>
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <div className="flex items-center gap-2 sm:gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/dashboard/programs")}
              className="flex-shrink-0"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-xl sm:text-2xl font-bold truncate">{program.name}</h1>
          </div>
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              disabled={isEditing}
              className="flex-1 sm:flex-none"
            >
              <Edit className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Edit</span>
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
              className="text-white hover:cursor-pointer flex-1 sm:flex-none"
            >
              {isDeleting ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent text-white" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4 text-white" />
              )}
              <span className="hidden sm:inline">Delete</span>
            </Button>
          </div>
        </div>

        <div className="grid gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Program Overview */}
          <div className="md:col-span-1 lg:col-span-1 space-y-4 sm:space-y-6">
            {/* Thumbnail */}
            <div className="rounded-lg border bg-card p-4 sm:p-6 shadow-sm">
              <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">Program Image</h3>
              <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                <img
                  src={program.thumbnail}
                  alt={program.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://via.placeholder.com/400x225?text=No+Image';
                  }}
                />
              </div>
            </div>

            {/* Program Stats */}
            <div className="rounded-lg border bg-card p-4 sm:p-6 shadow-sm">
              <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">Program Details</h3>
              <div className="space-y-3 sm:space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm text-muted-foreground">Type</span>
                  <span className="font-medium text-sm sm:text-base">{program.type}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm text-muted-foreground">Total Duration</span>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="font-medium text-sm sm:text-base">{program.duration} minutes</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm text-muted-foreground">Exercises</span>
                  <div className="flex items-center gap-1">
                    <Dumbbell className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="font-medium text-sm sm:text-base">{program.exercises?.length || 0} exercises</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm text-muted-foreground">subscriber</span>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="font-medium text-sm sm:text-base">{program.subscriber?.length || 0} users</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm text-muted-foreground">Created</span>
                  <span className="font-medium text-sm sm:text-base">
                    {program.createdAt?.toDate().toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Exercises List */}
          <div className="md:col-span-1 lg:col-span-2">
            <div className="rounded-lg border bg-card p-4 sm:p-6 shadow-sm">
              <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">
                Exercises ({program.exercises?.length || 0})
              </h3>

              {program.exercises && program.exercises.length > 0 ? (
                <div className="space-y-3 sm:space-y-4">
                  {program.exercises.map((exercise, index) => (
                    <div key={exercise.id} className="border rounded-lg p-3 sm:p-4">
                      <div className="flex flex-col sm:flex-row items-start gap-3 sm:gap-4">
                        {/* Exercise Number and Thumbnail Row for Mobile */}
                        <div className="flex items-center gap-3 w-full sm:w-auto">
                          {/* Exercise Number */}
                          <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs sm:text-sm font-medium">
                            {index + 1}
                          </div>

                          {/* Exercise Thumbnail */}
                          <div className="flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden bg-gray-100">
                            <img
                              src={exercise.thumbnail}
                              alt={exercise.exerciseName}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'https://via.placeholder.com/80x80?text=No+Image';
                              }}
                            />
                          </div>

                          {/* Exercise Actions for Mobile */}
                          <div className="flex items-center gap-1 ml-auto sm:hidden">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/dashboard/exercises/${exercise.id}`)}
                              className="p-2"
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                            {exercise.videoUrl && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open(exercise.videoUrl, '_blank')}
                                className="p-2"
                              >
                                <Play className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </div>

                        {/* Exercise Details */}
                        <div className="flex-1 min-w-0 w-full sm:w-auto">
                          <h4 className="font-medium text-base sm:text-lg mb-1 sm:mb-2">{exercise.exerciseName}</h4>
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-muted-foreground">
                            <span className="bg-secondary px-2 py-1 rounded text-xs w-fit">
                              {exercise.type}
                            </span>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{exercise.duration} minutes</span>
                            </div>
                          </div>
                        </div>

                        {/* Exercise Actions for Desktop */}
                        <div className="hidden sm:flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/dashboard/exercises/${exercise.id}`)}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                          {exercise.videoUrl && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(exercise.videoUrl, '_blank')}
                            >
                              <Play className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 sm:py-8">
                  <Dumbbell className="mx-auto h-8 w-8 sm:h-12 sm:w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No exercises</h3>
                  <p className="mt-1 text-xs sm:text-sm text-gray-500">
                    This program doesn't have any exercises yet.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
