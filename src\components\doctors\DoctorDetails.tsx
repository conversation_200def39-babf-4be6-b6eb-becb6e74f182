interface WorkExperience {
  years?: number;
  patients?: number;
  specialization?: string[] | string;
}

interface DoctorDetailsProps {
  doctor?: {
    introduction?: string;
    workExperience?: WorkExperience;
  };
}

export default function DoctorDetails({ doctor }: DoctorDetailsProps) {
  if (!doctor) {
    return (
      <div className="text-sm text-muted-foreground">
        No doctor information available
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="grid grid-cols-1 gap-2">
        <div className="text-sm font-medium">Introduction</div>
        <div className="text-sm">
          {doctor.introduction || 'No introduction provided'}
        </div>
      </div>
      {doctor.workExperience && (
        <>
          <div className="mt-4 grid grid-cols-2 gap-2">
            <div className="text-sm font-medium">Years of Experience</div>
            <div className="text-sm">
              {doctor.workExperience.years || 'Not specified'}
            </div>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div className="text-sm font-medium">Patients Treated</div>
            <div className="text-sm">
              {doctor.workExperience.patients || 'Not specified'}
            </div>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div className="text-sm font-medium">Specialization</div>
            <div className="text-sm">
              {(() => {
                const specialization = doctor.workExperience.specialization;

                // Handle array of specializations
                if (
                  Array.isArray(specialization) &&
                  specialization.length > 0
                ) {
                  return (
                    <div className="flex flex-wrap gap-1">
                      {specialization.map((spec, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center rounded-md bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                        >
                          {spec}
                        </span>
                      ))}
                    </div>
                  );
                }
                // Handle comma-separated string
                else if (
                  typeof specialization === 'string' &&
                  specialization.includes(',')
                ) {
                  const specs = specialization
                    .split(',')
                    .map((s) => s.trim())
                    .filter(Boolean);
                  return (
                    <div className="flex flex-wrap gap-1">
                      {specs.map((spec, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center rounded-md bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                        >
                          {spec}
                        </span>
                      ))}
                    </div>
                  );
                }
                // Handle single string or undefined
                return specialization || 'Not specified';
              })()}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
