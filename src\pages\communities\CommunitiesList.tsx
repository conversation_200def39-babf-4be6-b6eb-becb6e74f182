import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getCommunities } from "@/services/communityService";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Eye, Plus, MessageSquare } from "lucide-react";
import CommunityForm from "@/components/communities/CommunityForm";

export default function CommunitiesList() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddForm, setShowAddForm] = useState(false);

  // Fetch communities
  const {
    data: communities,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["communities"],
    queryFn: getCommunities,
  });

  // Filter communities based on search term
  const filteredCommunities = communities?.filter((community) =>
    community.topicName.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <DashboardLayout title="Communities">
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <h1 className="text-2xl font-bold">Communities</h1>
          <div className="flex items-center gap-2">
            <Button onClick={() => setShowAddForm(true)} className="w-full sm:w-auto">
              <Plus className="mr-2 h-4 w-4" />
              Add Community
            </Button>
          </div>
        </div>

        {/* Search and filters */}
        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search communities..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Add Community Form */}
        {showAddForm && (
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-semibold w-full sm:w-auto text-center">Add New Community</h2>
            <CommunityForm
              onCancel={() => setShowAddForm(false)}
              onSuccess={() => {
                setShowAddForm(false);
                queryClient.invalidateQueries({ queryKey: ["communities"] });
              }}
            />
          </div>
        )}

        {/* Communities List */}
        <div className="rounded-lg border bg-card shadow-sm">
          <div className="p-6">
            {isLoading ? (
              <div className="text-center">Loading communities...</div>
            ) : isError ? (
              <div className="text-center text-red-500">
                Error loading communities: {error instanceof Error ? error.message : "Unknown error"}
              </div>
            ) : filteredCommunities && filteredCommunities.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="px-4 py-3 text-left text-sm font-medium">Topic Name</th>
                      <th className="px-4 py-3 text-left text-sm font-medium">Members</th>
                      <th className="px-4 py-3 text-left text-sm font-medium">Created At</th>
                      <th className="px-4 py-3 text-right text-sm font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCommunities.map((community) => (
                      <tr key={community.id} className="border-b transition-colors hover:bg-muted/50">
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            <MessageSquare className="mr-2 h-4 w-4 text-primary" />
                            <span className="font-medium">{community.topicName}</span>
                          </div>
                        </td>
                        <td className="px-4 py-3">{community.currentMembers?.length || 0} members</td>
                        <td className="px-4 py-3">
                          {community.createdAt && typeof community.createdAt.toDate === "function"
                            ? new Date(community.createdAt.toDate()).toLocaleDateString()
                            : "N/A"}
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => navigate(`/dashboard/communities/${community.id}`)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center">
                {searchTerm ? "No communities found matching your search." : "No communities found."}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
