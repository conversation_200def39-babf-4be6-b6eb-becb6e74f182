import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import ProgramAssignmentForm from './ProgramAssignmentForm';

interface ProgramAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  patientId: string;
  patientName: string;
  onSuccess: () => void;
}

export default function ProgramAssignmentModal({ 
  isOpen, 
  onClose, 
  patientId,
  patientName,
  onSuccess 
}: ProgramAssignmentModalProps) {
  const handleSuccess = () => {
    onSuccess();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-3xl max-h-[90vh] overflow-y-auto sm:w-full">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl">
            Assign Program to Patient
          </DialogTitle>
        </DialogHeader>
        <ProgramAssignmentForm
          patientId={patientId}
          patientName={patientName}
          onCancel={onClose}
          onSuccess={handleSuccess}
        />
      </DialogContent>
    </Dialog>
  );
}
