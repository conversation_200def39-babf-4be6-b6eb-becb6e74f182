import type { ReactNode } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import owLogo from "../../../public/ow_logo.png";
// Import icons
import { Users, LogOut, Menu, X, Home, Settings, UserCheck, UserPlus, MessageSquare, Dumbbell, FolderOpen } from "lucide-react";
import { useState } from "react";

interface SidebarItemProps {
  icon: ReactNode;
  label: string;
  href?: string;
  active?: boolean;
  onClick?: () => void;
}

const SidebarItem = ({ icon, label, href, active, onClick }: SidebarItemProps) => {
  const navigate = useNavigate();

  return (
    <button
      onClick={onClick || (() => href && navigate(href))}
      className={`flex w-full items-center space-x-3 rounded-md px-3 py-2 text-sm ${
        active ? "bg-white text-primary font-medium" : "text-white hover:bg-white/10 hover:text-white"
      }`}
    >
      <span className="text-lg">{icon}</span>
      <span>{label}</span>
    </button>
  );
};

interface DashboardLayoutProps {
  children: ReactNode;
  title: string;
}

export default function DashboardLayout({ children, title }: DashboardLayoutProps) {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [usersExpanded, setUsersExpanded] = useState(false);
  const [communitiesExpanded, setCommunitiesExpanded] = useState(false);
  const [exercisesExpanded, setExercisesExpanded] = useState(false);

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  // Determine active route
  const pathname = window.location.pathname;

  return (
    <div className="flex h-screen bg-background">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed inset-y-0 left-0 z-50 w-64 transform border-r border-primary/20 bg-primary transition-transform duration-200 ease-in-out lg:static lg:translate-x-0 ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="flex h-10 items-center justify-between border-b border-primary/20 px-3">
          <div className="flex items-center">
            <img src={owLogo} alt="OW Logo" className="h-9 mt-5 w-auto object-contain" />
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-white hover:bg-primary-foreground/10 hover:text-white"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        <div className="space-y-4 py-4">
          <div className="px-3 py-2">
            <h2 className="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-white/70">Dashboard</h2>
            <div className="space-y-1">
              <SidebarItem
                icon={<Home className="h-5 w-5" />}
                label="Overview"
                href="/dashboard"
                active={pathname === "/dashboard"}
              />
              <SidebarItem
                icon={<Users className="h-5 w-5" />}
                label="Users"
                onClick={() => setUsersExpanded(!usersExpanded)}
                active={pathname === "/dashboard/users"}
              />
              {usersExpanded && (
                <div className="ml-6 space-y-1 border-l border-white/20 pl-2">
                  <SidebarItem
                    icon={<UserCheck className="h-5 w-5" />}
                    label="Patients"
                    href="/dashboard/users/patients"
                    active={pathname === "/dashboard/users/patients"}
                  />
                  <SidebarItem
                    icon={<UserPlus className="h-5 w-5" />}
                    label="Doctors"
                    href="/dashboard/users/doctors"
                    active={pathname === "/dashboard/users/doctors"}
                  />
                </div>
              )}
              <SidebarItem
                icon={<MessageSquare className="h-5 w-5" />}
                label="Communities"
                onClick={() => setCommunitiesExpanded(!communitiesExpanded)}
                active={pathname.startsWith("/dashboard/communities")}
              />
              {communitiesExpanded && (
                <div className="ml-6 space-y-1 border-l border-white/20 pl-2">
                  <SidebarItem
                    icon={<MessageSquare className="h-5 w-5" />}
                    label="All Communities"
                    href="/dashboard/communities"
                    active={pathname === "/dashboard/communities"}
                  />
                </div>
              )}
              <SidebarItem
                icon={<Dumbbell className="h-5 w-5" />}
                label="Exercises"
                onClick={() => setExercisesExpanded(!exercisesExpanded)}
                active={pathname.startsWith("/dashboard/exercises")}
              />
              {exercisesExpanded && (
                <div className="ml-6 space-y-1 border-l border-white/20 pl-2">
                  <SidebarItem
                    icon={<Dumbbell className="h-5 w-5" />}
                    label="All Exercises"
                    href="/dashboard/exercises"
                    active={pathname === "/dashboard/exercises"}
                  />
                </div>
              )}
              <SidebarItem
                icon={<FolderOpen className="h-5 w-5" />}
                label="Programs"
                href="/dashboard/programs"
                active={pathname.startsWith("/dashboard/programs")}
              />
              <SidebarItem
                icon={<Settings className="h-5 w-5" />}
                label="Settings"
                href="/dashboard/settings"
                active={pathname === "/dashboard/settings"}
              />
            </div>
          </div>
        </div>

        <div className="absolute bottom-0 w-full border-t border-primary/20 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-white">{"OW ADMIN"}</p>
              <p className="text-xs text-white/70">{user?.email}</p>
            </div>
            <Button variant="ghost" size="icon" onClick={handleLogout} className="text-white hover:bg-white/10">
              <LogOut className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Header */}
        <header className="flex h-16 items-center justify-between border-b bg-card px-4 lg:px-6">
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(true)} className="lg:hidden">
              <Menu className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-bold">{title}</h1>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-auto p-4 lg:p-6">{children}</main>
      </div>
    </div>
  );
}
