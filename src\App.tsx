import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Pages
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import UsersList from './pages/users/UsersList';
import UserDetail from './pages/users/UserDetail';
import Settings from './pages/Settings';
import Unauthorized from './pages/Unauthorized';
import CommunitiesList from './pages/communities/CommunitiesList';
import CommunityDetail from './pages/communities/CommunityDetail';
import ExercisesList from './pages/exercises/ExercisesList';
import ExerciseDetail from './pages/exercises/ExerciseDetail';
import ProgramsList from './pages/programs/ProgramsList';
import ProgramDetail from './pages/programs/ProgramDetail';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/unauthorized" element={<Unauthorized />} />

            {/* Protected routes */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/dashboard/users"
              element={
                <ProtectedRoute>
                  <UsersList />
                </ProtectedRoute>
              }
            />

            <Route
              path="/dashboard/users/doctors"
              element={
                <ProtectedRoute>
                  <UsersList userType="doctor" />
                </ProtectedRoute>
              }
            />

            <Route
              path="/dashboard/users/patients"
              element={
                <ProtectedRoute>
                  <UsersList userType="patient" />
                </ProtectedRoute>
              }
            />

            <Route
              path="/dashboard/users/:userId"
              element={
                <ProtectedRoute>
                  <UserDetail />
                </ProtectedRoute>
              }
            />

            <Route
              path="/dashboard/settings"
              element={
                <ProtectedRoute>
                  <Settings />
                </ProtectedRoute>
              }
            />

            {/* Communities routes */}
            <Route
              path="/dashboard/communities"
              element={
                <ProtectedRoute>
                  <CommunitiesList />
                </ProtectedRoute>
              }
            />

            <Route
              path="/dashboard/communities/:communityId"
              element={
                <ProtectedRoute>
                  <CommunityDetail />
                </ProtectedRoute>
              }
            />

            {/* Exercises routes */}
            <Route
              path="/dashboard/exercises"
              element={
                <ProtectedRoute>
                  <ExercisesList />
                </ProtectedRoute>
              }
            />

            <Route
              path="/dashboard/exercises/:exerciseId"
              element={
                <ProtectedRoute>
                  <ExerciseDetail />
                </ProtectedRoute>
              }
            />

            {/* Programs routes */}
            <Route
              path="/dashboard/programs"
              element={
                <ProtectedRoute>
                  <ProgramsList />
                </ProtectedRoute>
              }
            />

            <Route
              path="/dashboard/programs/:programId"
              element={
                <ProtectedRoute>
                  <ProgramDetail />
                </ProtectedRoute>
              }
            />

            {/* Redirect root to dashboard */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />

            {/* Catch all - redirect to dashboard */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
