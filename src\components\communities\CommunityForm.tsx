import { type Community, createCommunity, updateCommunity, checkTopicNameExists } from '@/services/communityService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState, useEffect, useCallback } from 'react';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';

interface CommunityFormProps {
  community?: Community;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function CommunityForm({ community, onSuccess, onCancel }: CommunityFormProps) {
  const [topicName, setTopicName] = useState(community?.topicName || '');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCheckingDuplicate, setIsCheckingDuplicate] = useState(false);
  const [isDuplicate, setIsDuplicate] = useState(false);
  const [isValidName, setIsValidName] = useState(false);

  // Debounced function to check for duplicate topic names
  const checkDuplicate = useCallback(async (name: string) => {
    if (!name.trim() || name.length < 2) {
      setIsDuplicate(false);
      setIsValidName(false);
      return;
    }

    setIsCheckingDuplicate(true);
    try {
      const exists = await checkTopicNameExists(name.trim(), community?.id);
      setIsDuplicate(exists);
      setIsValidName(!exists && name.trim().length > 0);
    } catch (error) {
      console.error('Error checking duplicate:', error);
      setIsDuplicate(false);
      setIsValidName(false);
    } finally {
      setIsCheckingDuplicate(false);
    }
  }, [community?.id]);

  // Debounce the duplicate check
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (topicName.trim()) {
        checkDuplicate(topicName);
      } else {
        setIsDuplicate(false);
        setIsValidName(false);
        setIsCheckingDuplicate(false);
      }
    }, 500); // 500ms delay

    return () => clearTimeout(timeoutId);
  }, [topicName, checkDuplicate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!topicName.trim()) {
      setError('Topic name is required');
      return;
    }

    if (isDuplicate) {
      setError('A community with this topic name already exists');
      return;
    }

    if (topicName.trim().length < 2) {
      setError('Topic name must be at least 2 characters long');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (community?.id) {
        // Update existing community
        const updateData = {
          topicName: topicName.trim(),
        };

        await updateCommunity(community.id, updateData);
      } else {
        // Create new community
        const newCommunityData = {
          topicName: topicName.trim(),
          currentMembers: []
        };

        await createCommunity(newCommunityData);
      }

      onSuccess();
    } catch (err) {
      console.error(`Error ${community?.id ? 'updating' : 'creating'} community:`, err);
      setError(err instanceof Error ? err.message : `Failed to ${community?.id ? 'update' : 'create'} community`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <label htmlFor="topicName" className="block text-sm font-medium text-gray-700">
              Topic Name
            </label>
            <div className="relative">
              <Input
                type="text"
                id="topicName"
                value={topicName}
                onChange={(e) => {
                  setTopicName(e.target.value);
                  if (error) setError(null); // Clear error when user starts typing
                }}
                required
                className={`mt-1 pr-10 ${
                  isDuplicate ? 'border-red-500 focus:border-red-500' :
                  isValidName ? 'border-green-500 focus:border-green-500' : ''
                }`}
                placeholder="Enter community topic name"
                disabled={isLoading}
                minLength={2}
              />

              {/* Validation icons */}
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {isCheckingDuplicate ? (
                  <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                ) : isDuplicate ? (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                ) : isValidName ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : null}
              </div>
            </div>

            {/* Real-time validation messages */}
            {topicName.trim() && !isCheckingDuplicate && (
              <div className="mt-1">
                {isDuplicate ? (
                  <p className="text-xs text-red-600">This topic name is already taken</p>
                ) : isValidName ? (
                  <p className="text-xs text-green-600">Topic name is available</p>
                ) : topicName.trim().length < 2 ? (
                  <p className="text-xs text-gray-500">Topic name must be at least 2 characters</p>
                ) : null}
              </div>
            )}
          </div>

          {error && (
            <div className="flex items-center gap-2 text-sm text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              isLoading ||
              isDuplicate ||
              !topicName.trim() ||
              topicName.trim().length < 2 ||
              isCheckingDuplicate
            }
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {community ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              community ? 'Update Community' : 'Create Community'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
