import { type Program, createProgram, updateProgram, checkProgramNameExists } from '@/services/programService';
import { createAndAssignProgram } from '@/services/assignedProgramService';
import { getExercises } from '@/services/exerciseService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState, useEffect, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Loader2, AlertCircle, CheckCircle, } from 'lucide-react';

interface ProgramFormProps {
  program?: Program;
  patientId?: string;
  patientName?: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function ProgramForm({ program, patientId, onSuccess, onCancel }: ProgramFormProps) {
  const [name, setName] = useState(program?.name || '');
  const [thumbnail, setThumbnail] = useState(program?.thumbnail || '');
  const [type, setType] = useState(program?.type || '');
  const [selectedExercises, setSelectedExercises] = useState<string[]>(program?.exercises || []);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCheckingDuplicate, setIsCheckingDuplicate] = useState(false);
  const [isDuplicate, setIsDuplicate] = useState(false);
  const [isValidName, setIsValidName] = useState(false);



  // Fetch all exercises
  const {
    data: exercises,
    isLoading: exercisesLoading,
    isError: exercisesError,
  } = useQuery({
    queryKey: ["exercises"],
    queryFn: getExercises,
  });

  // Debounced function to check for duplicate program names
  const checkDuplicate = useCallback(async (programName: string) => {
    if (!programName.trim() || programName.length < 2) {
      setIsDuplicate(false);
      setIsValidName(false);
      return;
    }

    setIsCheckingDuplicate(true);
    try {
      const exists = await checkProgramNameExists(programName.trim(), program?.id);
      setIsDuplicate(exists);
      setIsValidName(!exists && programName.trim().length > 0);
    } catch (error) {
      console.error('Error checking duplicate:', error);
      setIsDuplicate(false);
      setIsValidName(false);
    } finally {
      setIsCheckingDuplicate(false);
    }
  }, [program?.id]);

  // Debounce the duplicate check
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (name.trim()) {
        checkDuplicate(name);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [name, checkDuplicate]);

  // Calculate total duration
  const calculateTotalDuration = () => {
    if (!exercises || !Array.isArray(exercises)) return 0;
    return selectedExercises.reduce((total, exerciseId) => {
      const exercise = exercises.find(ex => ex.id === exerciseId);
      return total + (exercise?.duration || 0);
    }, 0);
  };

  const handleExerciseToggle = (exerciseId: string) => {
    setSelectedExercises(prev => 
      prev.includes(exerciseId)
        ? prev.filter(id => id !== exerciseId)
        : [...prev, exerciseId]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validation
    if (!name.trim()) {
      setError('Program name is required');
      return;
    }

    if (isDuplicate) {
      setError('A program with this name already exists');
      return;
    }

    if (!thumbnail.trim()) {
      setError('Thumbnail URL is required');
      return;
    }

    if (!type.trim()) {
      setError('Program type is required');
      return;
    }

    if (selectedExercises.length === 0) {
      setError('At least one exercise must be selected');
      return;
    }

    setIsLoading(true);

    try {
      if (program?.id) {
        // Update existing program
        const updateData = {
          name: name.trim(),
          thumbnail: thumbnail.trim(),
          type: type.trim(),
          exercises: selectedExercises,
          subscriber: program.subscriber || [],
        };

        await updateProgram(program.id, updateData);
      } else {
        // Create new program
        const newProgramData = {
          name: name.trim(),
          thumbnail: thumbnail.trim(),
          type: type.trim(),
          exercises: selectedExercises,
          subscriber: [],
        };

        if (patientId) {
          // If patientId is provided, create and assign directly to AssignedPrograms collection
          const totalDuration = calculateTotalDuration();
          await createAndAssignProgram(newProgramData, patientId, totalDuration);
        } else {
          // Otherwise, create in Programs collection as usual
          await createProgram(newProgramData);
        }
      }

      onSuccess();
    } catch (err) {
      console.error(`Error ${program?.id ? 'updating' : 'creating'} program:`, err);
      setError(err instanceof Error ? err.message : `Failed to ${program?.id ? 'update' : 'create'} program`);
    } finally {
      setIsLoading(false);
    }
  };

  if (exercisesLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading exercises...</span>
      </div>
    );
  }

  if (exercisesError) {
    return (
      <div className="flex items-center justify-center p-8 text-red-600">
        <AlertCircle className="h-8 w-8" />
        <span className="ml-2">Failed to load exercises</span>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="flex items-center space-x-2 rounded-md border border-red-200 bg-red-50 p-3 text-red-700">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      {/* Patient Info - Show only when creating program for assignment */}
      {/* {patientId && patientName && (
        <div className="rounded-lg border bg-blue-50 p-4">
          <div className="flex items-center space-x-2">
            <User className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-900">
              Creating program for: {patientName}
            </span>
          </div>
          <p className="text-sm text-blue-700 mt-1">
            This program will be automatically assigned to the patient after creation.
          </p>
        </div>
      )} */}

      {/* Program Name */}
      <div className="space-y-2">
        <Label htmlFor="name">Program Name *</Label>
        <div className="relative">
          <Input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter program name"
            className={`pr-8 ${isDuplicate ? 'border-red-500' : isValidName ? 'border-green-500' : ''}`}
            required
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            {isCheckingDuplicate && <Loader2 className="h-4 w-4 animate-spin text-gray-400" />}
            {!isCheckingDuplicate && isDuplicate && <AlertCircle className="h-4 w-4 text-red-500" />}
            {!isCheckingDuplicate && isValidName && <CheckCircle className="h-4 w-4 text-green-500" />}
          </div>
        </div>
        {isDuplicate && (
          <p className="text-sm text-red-600">A program with this name already exists</p>
        )}
      </div>

      {/* Thumbnail URL */}
      <div className="space-y-2">
        <Label htmlFor="thumbnail">Thumbnail URL *</Label>
        <Input
          id="thumbnail"
          type="url"
          value={thumbnail}
          onChange={(e) => setThumbnail(e.target.value)}
          placeholder="Enter thumbnail image URL"
          required
        />
      </div>

      {/* Program Type */}
      <div className="space-y-2">
        <Label htmlFor="type">Program Type *</Label>
        <Input
          id="type"
          type="text"
          value={type}
          onChange={(e) => setType(e.target.value)}
          placeholder="Enter program type (e.g., Weight Loss, Muscle Building, Cardio)"
          required
        />
      </div>

      {/* Exercise Selection */}
      <div className="space-y-2">
        <Label>Select Exercises *</Label>
        <div className="rounded-md border p-3 sm:p-4 max-h-60 overflow-y-auto">
          {exercises && exercises.length > 0 ? (
            <div className="space-y-2">
              {exercises.map((exercise) => (
                <div key={exercise.id} className="flex items-start space-x-2 sm:items-center">
                  <input
                    type="checkbox"
                    id={`exercise-${exercise.id}`}
                    checked={selectedExercises.includes(exercise.id!)}
                    onChange={() => handleExerciseToggle(exercise.id!)}
                    className="rounded border-gray-300 mt-1 sm:mt-0 flex-shrink-0"
                  />
                  <label
                    htmlFor={`exercise-${exercise.id}`}
                    className="flex-1 text-sm cursor-pointer"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center">
                      <span className="font-medium">{exercise.exerciseName}</span>
                      <span className="text-gray-500 sm:ml-2 text-xs sm:text-sm">
                        ({exercise.duration} min, {exercise.type})
                      </span>
                    </div>
                  </label>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500">No exercises available</p>
          )}
        </div>
        <p className="text-xs sm:text-sm text-gray-600">
          Selected: {selectedExercises.length} exercises, Total duration: {calculateTotalDuration()} minutes
        </p>
      </div>

      {/* Form Actions */}
      <div className="flex flex-col-reverse sm:flex-row justify-end gap-2 sm:space-x-2 sm:gap-0">
        <Button type="button" variant="outline" onClick={onCancel} className="w-full sm:w-auto">
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading || isDuplicate || !isValidName} className="w-full sm:w-auto cursor-pointer">
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {program?.id ? 'Update Program' : 'Create Program'}
        </Button>
      </div>
    </form>
  );
}
