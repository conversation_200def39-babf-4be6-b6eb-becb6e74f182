import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Upload, X, File, Image, Video, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatFileSize, validateFile } from '@/services/fileUploadService';

export interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onFileRemove: () => void;
  onUrlChange?: (url: string) => void;
  accept: string;
  fileType: 'image' | 'video';
  currentFile?: File | null;
  currentUrl?: string | null;
  isUploading?: boolean;
  uploadProgress?: number;
  error?: string | null;
  disabled?: boolean;
  className?: string;
  label: string;
  description?: string;
  required?: boolean;
  allowUrlInput?: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFileSelect,
  onFileRemove,
  onUrlChange,
  accept,
  fileType,
  currentFile,
  currentUrl,
  isUploading = false,
  uploadProgress = 0,
  error,
  disabled = false,
  className,
  label,
  description,
  required = false,
  allowUrlInput = true,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [inputMode, setInputMode] = useState<'file' | 'url'>('file');

  const handleFileSelect = (file: File) => {
    const validation = validateFile(file, fileType);
    if (!validation.isValid) {
      return;
    }
    onFileSelect(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled || isUploading) return;

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const openFileDialog = () => {
    if (!disabled && !isUploading) {
      fileInputRef.current?.click();
    }
  };

  const getFileIcon = () => {
    if (fileType === 'image') return <Image className="h-8 w-8" />;
    if (fileType === 'video') return <Video className="h-8 w-8" />;
    return <File className="h-8 w-8" />;
  };

  const hasContent = currentFile || currentUrl;

  return (
    <div className={cn('space-y-2', className)}>
      <label className="block text-sm font-medium text-gray-700">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      {allowUrlInput && (
        <div className="flex gap-2 mb-2">
          <Button
            type="button"
            variant={inputMode === 'file' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setInputMode('file')}
            disabled={disabled}
          >
            Upload File
          </Button>
          <Button
            type="button"
            variant={inputMode === 'url' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setInputMode('url')}
            disabled={disabled}
          >
            Enter URL
          </Button>
        </div>
      )}

      {inputMode === 'url' && allowUrlInput ? (
        <div>
          <Input
            type="url"
            value={currentUrl || ''}
            onChange={(e) => onUrlChange?.(e.target.value)}
            placeholder={`Enter ${fileType} URL`}
            disabled={disabled}
            className="mt-1"
          />
          {description && (
            <p className="mt-1 text-xs text-gray-500">{description}</p>
          )}
        </div>
      ) : (
        <div>
          <div
            className={cn(
              'relative border-2 border-dashed rounded-lg p-6 transition-colors',
              dragActive && !disabled && !isUploading
                ? 'border-primary bg-primary/5'
                : 'border-gray-300',
              disabled || isUploading
                ? 'opacity-50 cursor-not-allowed'
                : 'cursor-pointer hover:border-primary hover:bg-primary/5',
              hasContent && 'border-solid border-gray-200 bg-gray-50'
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={!hasContent ? openFileDialog : undefined}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept={accept}
              onChange={handleFileInputChange}
              className="hidden"
              disabled={disabled || isUploading}
            />

            {isUploading ? (
              <div className="flex flex-col items-center justify-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-sm text-gray-600">Uploading...</p>
                {uploadProgress > 0 && (
                  <div className="w-full max-w-xs bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                )}
              </div>
            ) : hasContent ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-primary">{getFileIcon()}</div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {currentFile ? currentFile.name : 'URL provided'}
                    </p>
                    {currentFile && (
                      <p className="text-xs text-gray-500">
                        {formatFileSize(currentFile.size)}
                      </p>
                    )}
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onFileRemove();
                  }}
                  disabled={disabled}
                  className="text-gray-400 hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center space-y-2">
                <div className="text-gray-400">{getFileIcon()}</div>
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium text-primary">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">
                    {fileType === 'image' ? 'PNG, JPG, GIF up to 5MB' : 'MP4, WebM, OGG up to 100MB'}
                  </p>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  disabled={disabled}
                  onClick={(e) => {
                    e.stopPropagation();
                    openFileDialog();
                  }}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Choose File
                </Button>
              </div>
            )}
          </div>

          {description && !hasContent && (
            <p className="mt-1 text-xs text-gray-500">{description}</p>
          )}

          {error && (
            <p className="mt-1 text-xs text-red-600">{error}</p>
          )}
        </div>
      )}
    </div>
  );
};
