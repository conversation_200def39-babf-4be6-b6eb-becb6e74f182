interface ScheduleDay {
  day: string;
  from: string;
  to: string;
}

interface DoctorScheduleProps {
  schedule?: any;
}

export default function DoctorSchedule({ schedule }: DoctorScheduleProps) {
  if (!schedule || (Array.isArray(schedule) && schedule.length === 0)) {
    return <div className="text-sm text-muted-foreground">No schedule available</div>;
  }

  // Ensure schedule is an array of day objects
  let scheduleArray: ScheduleDay[] = [];

  if (Array.isArray(schedule)) {
    scheduleArray = schedule;
  } else if (typeof schedule === "object") {
    // Handle if schedule is an object with day keys
    scheduleArray = Object.entries(schedule).map(([day, time]) => {
      const timeObj = time as any;
      if (typeof time === "string" && time.includes("-")) {
        const [from, to] = time.split("-").map((t) => t.trim());
        return { day, from, to };
      }
      // Handle if time is an object with from/to properties
      else if (typeof time === "object" && time !== null && timeObj.from && timeObj.to) {
        return { day, from: timeObj.from, to: timeObj.to };
      }
      return { day, from: "N/A", to: "N/A" };
    });
  }

  // Sort days of the week in a standard order
  const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

  const sortedSchedule = [...scheduleArray].sort((a, b) => {
    return days.indexOf(a.day.toLowerCase()) - days.indexOf(b.day.toLowerCase());
  });

  return (
    <div className="space-y-2">
      {sortedSchedule.map((day, index) => (
        <div key={index} className="grid grid-cols-3 gap-2">
          <div className="text-sm font-medium capitalize">{day.day}</div>
          <div className="col-span-2 text-sm">
            {day.from} - {day.to}
          </div>
        </div>
      ))}
    </div>
  );
}
