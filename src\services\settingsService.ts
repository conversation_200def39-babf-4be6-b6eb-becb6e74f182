import api from '@/lib/api';

// Define the Settings interface
export interface Settings {
  _id: string;
  key: string;
  value: any;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Get all settings
export const getAllSettings = async (): Promise<Settings[]> => {
  const response = await api.get('/settings');
  return response.data;
};

// Get a single setting by key
export const getSettings = async (key: string): Promise<Settings> => {
  const response = await api.get(`/settings/${key}`);
  return response.data;
};

// Update a setting
export const updateSettings = async (key: string, value: any, description?: string): Promise<Settings> => {
  const response = await api.patch(`/settings/${key}`, { 
    value,
    ...(description && { description })
  });
  return response.data;
};
