import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Check, X, Pencil } from 'lucide-react';

interface EditableServicePriceProps {
  price: string;
  onSave: (newPrice: string) => void;
  onCancel: () => void;
  isEditing: boolean;
  onEdit: () => void;
}

export default function EditableServicePrice({
  price,
  onSave,
  onCancel,
  isEditing,
  onEdit,
}: EditableServicePriceProps) {
  const [editedPrice, setEditedPrice] = useState(price);

  const handleSave = () => {
    // Remove any non-numeric characters except decimal point
    const cleanedPrice = editedPrice.replace(/[^0-9.]/g, '');
    
    // Ensure it's a valid number
    if (!isNaN(parseFloat(cleanedPrice))) {
      onSave(cleanedPrice);
    } else {
      // If invalid, revert to original price
      setEditedPrice(price);
      onCancel();
    }
  };

  const handleCancel = () => {
    setEditedPrice(price);
    onCancel();
  };

  if (isEditing) {
    return (
      <div className="flex items-center space-x-1">
        <div className="relative">
          <span className="absolute left-2 top-1/2 -translate-y-1/2 text-xs">$</span>
          <Input
            value={editedPrice}
            onChange={(e) => setEditedPrice(e.target.value)}
            className="w-20 pl-6 py-1 h-7 text-xs"
            autoFocus
          />
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={handleSave}
        >
          <Check className="h-4 w-4 text-green-600" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={handleCancel}
        >
          <X className="h-4 w-4 text-red-600" />
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center">
      <span className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
        ${price}
      </span>
      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6 ml-1"
        onClick={onEdit}
      >
        <Pencil className="h-3 w-3 text-muted-foreground" />
      </Button>
    </div>
  );
}
