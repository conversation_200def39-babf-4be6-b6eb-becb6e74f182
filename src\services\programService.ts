import {
  Timestamp,
  collection,
  addDoc,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Collections } from '@/constant';
import { getExerciseById, type Exercise } from './exerciseService';

// Define the Program interface
export interface Program {
  id?: string;
  name: string;
  subscriber: string[]; // Array of user IDs
  thumbnail: string; // URL to thumbnail image
  exercises: string[]; // Array of exercise IDs
  type: string; // Program type (e.g., "weight-loss", "muscle-building", "flexibility")
  duration: number; // Total duration in minutes (calculated from exercises)
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Program with populated exercises for display
export interface ProgramWithExercises extends Omit<Program, 'exercises'> {
  exercises: Exercise[];
}

// Get all programs
export const getPrograms = async (): Promise<Program[]> => {
  try {
    const programsRef = collection(db, Collections.PROGRAMS);
    const q = query(programsRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);

    const programs: Program[] = [];
    querySnapshot.forEach((doc) => {
      programs.push({
        id: doc.id,
        ...doc.data()
      } as Program);
    });

    return programs;
  } catch (error) {
    console.error('Error fetching programs:', error);
    throw new Error('Failed to fetch programs');
  }
};

// Get a single program by ID
export const getProgramById = async (id: string): Promise<Program | null> => {
  try {
    const programRef = doc(db, Collections.PROGRAMS, id);
    const programSnap = await getDoc(programRef);

    if (programSnap.exists()) {
      return {
        id: programSnap.id,
        ...programSnap.data()
      } as Program;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error fetching program:', error);
    throw new Error('Failed to fetch program');
  }
};

// Get program with populated exercises
export const getProgramWithExercises = async (id: string): Promise<ProgramWithExercises | null> => {
  try {
    const program = await getProgramById(id);
    if (!program) return null;

    // Fetch all exercises for this program
    const exercises: Exercise[] = [];
    for (const exerciseId of program.exercises) {
      const exercise = await getExerciseById(exerciseId);
      if (exercise) {
        exercises.push(exercise);
      }
    }

    return {
      ...program,
      exercises
    };
  } catch (error) {
    console.error('Error fetching program with exercises:', error);
    throw new Error('Failed to fetch program with exercises');
  }
};

// Check if a program name already exists
export const checkProgramNameExists = async (programName: string, excludeId?: string): Promise<boolean> => {
  try {
    const programsRef = collection(db, Collections.PROGRAMS);
    const q = query(
      programsRef,
      where('name', '==', programName.trim())
    );
    const querySnapshot = await getDocs(q);

    // If excludeId is provided (for updates), check if any other document has this program name
    if (excludeId) {
      return querySnapshot.docs.some(doc => doc.id !== excludeId);
    }

    // For new programs, any existing document with this program name is a duplicate
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking program name:', error);
    throw new Error('Failed to check program name');
  }
};

// Calculate total duration from exercises
export const calculateProgramDuration = async (exerciseIds: string[]): Promise<number> => {
  try {
    let totalDuration = 0;
    
    for (const exerciseId of exerciseIds) {
      const exercise = await getExerciseById(exerciseId);
      if (exercise) {
        totalDuration += exercise.duration;
      }
    }
    
    return totalDuration;
  } catch (error) {
    console.error('Error calculating program duration:', error);
    return 0;
  }
};

// Create a new program
export const createProgram = async (programData: Omit<Program, 'id' | 'createdAt' | 'updatedAt' | 'duration'>): Promise<Program> => {
  try {
    // Check if program name already exists
    const nameExists = await checkProgramNameExists(programData.name);
    if (nameExists) {
      throw new Error('A program with this name already exists');
    }

    // Calculate duration from exercises
    const duration = await calculateProgramDuration(programData.exercises);

    const now = Timestamp.now();
    const newProgramData = {
      ...programData,
      duration,
      createdAt: now,
      updatedAt: now,
    };

    const programsRef = collection(db, Collections.PROGRAMS);
    const docRef = await addDoc(programsRef, newProgramData);

    return {
      id: docRef.id,
      ...newProgramData,
    };
  } catch (error) {
    console.error('Error creating program:', error);
    // Re-throw the error with the original message if it's our custom validation error
    if (error instanceof Error && error.message === 'A program with this name already exists') {
      throw error;
    }
    throw new Error('Failed to create program');
  }
};

// Update an existing program
export const updateProgram = async (id: string, programData: Partial<Omit<Program, 'id' | 'createdAt' | 'updatedAt' | 'duration'>>): Promise<void> => {
  try {
    // Check if program name already exists (excluding current program)
    if (programData.name) {
      const nameExists = await checkProgramNameExists(programData.name, id);
      if (nameExists) {
        throw new Error('A program with this name already exists');
      }
    }

    // Calculate duration if exercises are being updated
    let duration: number | undefined;
    if (programData.exercises) {
      duration = await calculateProgramDuration(programData.exercises);
    }

    const programRef = doc(db, Collections.PROGRAMS, id);
    const updateData = {
      ...programData,
      ...(duration !== undefined && { duration }),
      updatedAt: Timestamp.now(),
    };

    await updateDoc(programRef, updateData);
  } catch (error) {
    console.error('Error updating program:', error);
    // Re-throw the error with the original message if it's our custom validation error
    if (error instanceof Error && error.message === 'A program with this name already exists') {
      throw error;
    }
    throw new Error('Failed to update program');
  }
};

// Delete a program
export const deleteProgram = async (id: string): Promise<void> => {
  try {
    const programRef = doc(db, Collections.PROGRAMS, id);
    await deleteDoc(programRef);
  } catch (error) {
    console.error('Error deleting program:', error);
    throw new Error('Failed to delete program');
  }
};
